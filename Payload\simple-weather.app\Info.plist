<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>UIRequiredDeviceCapabilities</key>
    <array>
      <string>arm64</string>
    </array>
    <key>DTCompiler</key>
    <string>com.apple.compilers.llvm.clang.1_0</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>DTPlatformVersion</key>
    <string>18.2</string>
    <key>UIUserInterfaceStyle</key>
    <string>Light</string>
    <key>CFBundleName</key>
    <string>simple-weather</string>
    <key>DTSDKName</key>
    <string>iphoneos18.2</string>
    <key>NSUserActivityTypes</key>
    <array>
      <string>ConfigurationIntent</string>
    </array>
    <key>DTAppStoreToolsBuild</key>
    <string>16C7015</string>
    <key>ICLOUD_KEY_PREFIX</key>
    <string>production</string>
    <key>APP_GROUP_NAME</key>
    <string>group.com.andyworks.weather</string>
    <key>CFBundleIcons</key>
    <dict>
      <key>CFBundleAlternateIcons</key>
      <dict>
        <key>cedarAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>cedarAppIcon</string>
        </dict>
        <key>karatAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>karatAppIcon</string>
        </dict>
        <key>normalAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>normalAppIcon</string>
        </dict>
        <key>chromaAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>chromaAppIcon</string>
        </dict>
        <key>monsterAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>monsterAppIcon</string>
        </dict>
        <key>wireframeAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>wireframeAppIcon</string>
        </dict>
        <key>opalAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>opalAppIcon</string>
        </dict>
        <key>bricksAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>bricksAppIcon</string>
        </dict>
        <key>depthAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>depthAppIcon</string>
        </dict>
        <key>andyAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>andyAppIcon</string>
        </dict>
        <key>presstubeAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>presstubeAppIcon</string>
        </dict>
      </dict>
      <key>CFBundlePrimaryIcon</key>
      <dict>
        <key>CFBundleIconFiles</key>
        <array>
          <string>AppIcon60x60</string>
        </array>
        <key>CFBundleIconName</key>
        <string>AppIcon</string>
      </dict>
    </dict>
    <key>CONFIG_URL</key>
    <string></string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>CFBundleDisplayName</key>
    <string></string>
    <key>LSApplicationQueriesSchemes</key>
    <array>
      <string>com.andyworks.timer</string>
      <string>com.andyworks.calculator</string>
      <string>com.andyworks.vibes</string>
      <string>com.andyworks.streaks</string>
    </array>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>Fetch the current weather at your location.</string>
    <key>DTSDKBuild</key>
    <string>22C146</string>
    <key>CFBundleShortVersionString</key>
    <string>3.38</string>
    <key>CFBundleSupportedPlatforms</key>
    <array>
      <string>iPhoneOS</string>
    </array>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
    </array>
    <key>BuildMachineOSBuild</key>
    <string>24D70</string>
    <key>DTPlatformBuild</key>
    <string>22C146</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>MinimumOSVersion</key>
    <string>17.0</string>
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>DTXcodeBuild</key>
    <string>16C5032a</string>
    <key>CFBundleVersion</key>
    <string>0</string>
    <key>USER_GROUP_NAME</key>
    <string>group.com.andyworks.userData</string>
    <key>NSPhotoLibraryAddUsageDescription</key>
    <string>Save wallpapers to your photo library</string>
    <key>UIAppFonts</key>
    <array>
      <string>JetBrainsMono-ExtraBold.ttf</string>
      <string>JetBrainsMono-Regular.ttf</string>
      <string>NeueMaticCompressed-Bold.otf</string>
      <string>NeumaticGothic-Regular.otf</string>
      <string>NeumaticGothic-Bold.otf</string>
      <string>FoundersGrotesk-BoldItalic.otf</string>
      <string>FoundersGrotesk-Regular.otf</string>
      <string>FoundersGrotesk-RegularItalic.otf</string>
      <string>FoundersGrotesk-Bold.otf</string>
    </array>
    <key>UIDeviceFamily</key>
    <array>
      <integer>1</integer>
      <integer>2</integer>
    </array>
    <key>UILaunchStoryboardName</key>
    <string>Launch Screen</string>
    <key>CFBundleIdentifier</key>
    <string>com.andyworks.weather</string>
    <key>ICLOUD_SHOWNREDEEM_KEY_NAME</key>
    <string>shownRedeem</string>
    <key>SUBSCRIPTION_SERVER_BASE_URL</key>
    <string>api-weather.andy.works</string>
    <key>UIApplicationSceneManifest</key>
    <dict>
      <key>UIApplicationSupportsMultipleScenes</key>
      <false/>
      <key>UISceneConfigurations</key>
      <dict>
        <key>UIWindowSceneSessionRoleApplication</key>
        <array>
          <dict>
            <key>UISceneStoryboardFile</key>
            <string>Main</string>
            <key>UISceneConfigurationName</key>
            <string>Default Configuration</string>
            <key>UISceneDelegateClassName</key>
            <string>simple_weather.SceneDelegate</string>
          </dict>
        </array>
      </dict>
    </dict>
    <key>DTXcode</key>
    <string>1620</string>
    <key>CFBundleExecutable</key>
    <string>simple-weather</string>
    <key>AUTH_TOKEN_KEY_NAME</key>
    <string>authToken</string>
    <key>NSAppTransportSecurity</key>
    <dict>
      <key>NSAllowsArbitraryLoads</key>
      <true/>
    </dict>
    <key>CFBundleIcons~ipad</key>
    <dict>
      <key>CFBundleAlternateIcons</key>
      <dict>
        <key>cedarAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>cedarAppIcon</string>
        </dict>
        <key>karatAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>karatAppIcon</string>
        </dict>
        <key>normalAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>normalAppIcon</string>
        </dict>
        <key>chromaAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>chromaAppIcon</string>
        </dict>
        <key>monsterAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>monsterAppIcon</string>
        </dict>
        <key>wireframeAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>wireframeAppIcon</string>
        </dict>
        <key>opalAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>opalAppIcon</string>
        </dict>
        <key>bricksAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>bricksAppIcon</string>
        </dict>
        <key>depthAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>depthAppIcon</string>
        </dict>
        <key>andyAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>andyAppIcon</string>
        </dict>
        <key>presstubeAppIcon</key>
        <dict>
          <key>CFBundleIconName</key>
          <string>presstubeAppIcon</string>
        </dict>
      </dict>
      <key>CFBundlePrimaryIcon</key>
      <dict>
        <key>CFBundleIconFiles</key>
        <array>
          <string>AppIcon60x60</string>
          <string>AppIcon76x76</string>
        </array>
        <key>CFBundleIconName</key>
        <string>AppIcon</string>
      </dict>
    </dict>
    <key>UIMainStoryboardFile</key>
    <string>Main</string>
    <key>REVENUECAT_APPGROUP</key>
    <string>group.com.andyworks.weather-revenuecat</string>
    <key>DTPlatformName</key>
    <string>iphoneos</string>
    <key>ITSAppUsesNonExemptEncryption</key>
    <false/>
    <key>CFBundleURLTypes</key>
    <array>
      <dict>
        <key>CFBundleTypeRole</key>
        <string>Editor</string>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>com.andyworks.weather</string>
        </array>
      </dict>
    </array>
  </dict>
</plist>
