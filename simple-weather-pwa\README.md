# Simple Weather PWA

A beautiful 3D weather Progressive Web App inspired by the iOS Simple Weather app by <PERSON>.

## Features

- 🌤️ **3D Weather Visualization** - Interactive 3D scenes with weather-specific particles and effects
- 🎨 **Multiple Themes** - 8 beautiful themes (Normal, <PERSON>, <PERSON><PERSON>, <PERSON>, Chroma, Depth, G<PERSON>hite, Karat)
- 📱 **Progressive Web App** - Install on any device, works offline
- 🌍 **Location-based Weather** - Automatic location detection with fallback
- ⚡ **Real-time Updates** - Live weather data with background sync
- 📊 **Detailed Metrics** - Temperature, humidity, wind, pressure, UV index, and more
- 📈 **Hourly Forecast** - Next 8 hours weather prediction
- 🔄 **Offline Support** - Cached data when network is unavailable

## Technology Stack

- **React 18** - Modern React with hooks
- **Three.js** - 3D graphics and animations
- **@react-three/fiber** - React renderer for Three.js
- **Vite** - Fast build tool and dev server
- **PWA** - Service worker, manifest, offline support

## Getting Started

### Prerequisites

- Node.js 16+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd simple-weather-pwa
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser to `http://localhost:3000`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

### PWA Installation

1. Open the app in a modern browser
2. Look for the "Install" prompt or use browser menu
3. The app will be installed as a native-like application

## Project Structure

```
simple-weather-pwa/
├── public/
│   ├── manifest.json          # PWA manifest
│   ├── sw.js                  # Service worker
│   └── icons/                 # App icons
├── src/
│   ├── components/
│   │   ├── WeatherScene.jsx   # 3D weather visualization
│   │   ├── WeatherUI.jsx      # Main UI overlay
│   │   ├── ThemeSelector.jsx  # Theme selection modal
│   │   └── LoadingScreen.jsx  # Loading component
│   ├── hooks/
│   │   ├── useWeatherData.js  # Weather data fetching
│   │   └── useGeolocation.js  # Location services
│   ├── data/
│   │   └── themes.js          # Theme configurations
│   ├── styles/
│   │   ├── index.css          # Global styles
│   │   ├── App.css            # Main app styles
│   │   └── ThemeSelector.css  # Theme selector styles
│   ├── App.jsx                # Main app component
│   └── main.jsx               # App entry point
├── package.json
├── vite.config.js             # Vite configuration
└── README.md
```

## Features in Detail

### 3D Weather Effects

The app uses Three.js to create immersive 3D weather visualizations:

- **Rain**: Animated droplets falling from the sky
- **Snow**: Gentle snowflakes with realistic physics
- **Clouds**: Volumetric cloud formations that drift slowly
- **Sun**: Glowing sun with pulsing animation
- **Particles**: Weather-specific particle systems

### Theme System

8 unique themes each with:
- Custom color palettes
- Particle configurations
- Background gradients
- Weather-specific styling

### PWA Features

- **Offline Support**: Works without internet connection
- **Install Prompt**: Can be installed on home screen
- **Background Sync**: Updates weather data in background
- **Push Notifications**: Weather alerts (future feature)
- **Responsive Design**: Works on all screen sizes

## Weather Data

Currently uses demo data for development. To integrate real weather data:

1. Get an API key from OpenWeatherMap or similar service
2. Uncomment the real API code in `src/hooks/useWeatherData.js`
3. Add your API key to environment variables

## Browser Support

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is inspired by the iOS Simple Weather app by Andy Works. This is a web recreation for educational purposes.

## Acknowledgments

- **Andy Works** - Original iOS Simple Weather app design and concept
- **Three.js** - 3D graphics library
- **React Three Fiber** - React integration for Three.js
- **OpenWeatherMap** - Weather data API (when integrated)
