// Weather themes based on the iOS app configuration
export const weatherThemes = {
  normal: {
    id: 'normal',
    name: 'Normal',
    colors: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.8)',
      accent: '#80f2b9',
      clouds: 'rgba(128, 13, 185, 0.5)',
      rain: 'rgba(128, 207, 229, 1)',
      snow: 'rgba(128, 128, 255, 1)',
      sun: 'rgba(242, 128, 185, 1)',
      bolt: 'rgba(128, 242, 185, 1)'
    },
    particles: {
      count: 100,
      size: 0.02,
      speed: 0.1
    }
  },
  
  andy: {
    id: 'andy',
    name: '<PERSON>',
    colors: {
      background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)',
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.9)',
      accent: '#ff6b9d',
      clouds: 'rgba(255, 255, 255, 0.6)',
      rain: 'rgba(173, 216, 230, 1)',
      snow: 'rgba(255, 255, 255, 1)',
      sun: 'rgba(255, 215, 0, 1)',
      bolt: 'rgba(255, 255, 0, 1)'
    },
    particles: {
      count: 80,
      size: 0.025,
      speed: 0.08
    }
  },

  bricks: {
    id: 'bricks',
    name: 'Bricks',
    colors: {
      background: 'linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #CD853F 100%)',
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.9)',
      accent: '#DEB887',
      clouds: 'rgba(139, 69, 19, 0.4)',
      rain: 'rgba(70, 130, 180, 1)',
      snow: 'rgba(255, 250, 250, 1)',
      sun: 'rgba(255, 140, 0, 1)',
      bolt: 'rgba(255, 255, 0, 1)'
    },
    particles: {
      count: 120,
      size: 0.03,
      speed: 0.12
    }
  },

  cedar: {
    id: 'cedar',
    name: 'Cedar',
    colors: {
      background: 'linear-gradient(135deg, #2F4F2F 0%, #228B22 50%, #32CD32 100%)',
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.9)',
      accent: '#90EE90',
      clouds: 'rgba(47, 79, 47, 0.5)',
      rain: 'rgba(0, 191, 255, 1)',
      snow: 'rgba(255, 255, 255, 1)',
      sun: 'rgba(255, 215, 0, 1)',
      bolt: 'rgba(255, 255, 0, 1)'
    },
    particles: {
      count: 90,
      size: 0.02,
      speed: 0.09
    }
  },

  chroma: {
    id: 'chroma',
    name: 'Chroma',
    colors: {
      background: 'linear-gradient(135deg, #ff0080 0%, #ff8c00 25%, #40e0d0 50%, #ee82ee 75%, #ff0080 100%)',
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.9)',
      accent: '#ff69b4',
      clouds: 'rgba(255, 255, 255, 0.3)',
      rain: 'rgba(64, 224, 208, 1)',
      snow: 'rgba(255, 255, 255, 1)',
      sun: 'rgba(255, 215, 0, 1)',
      bolt: 'rgba(255, 0, 255, 1)'
    },
    particles: {
      count: 150,
      size: 0.015,
      speed: 0.15
    }
  },

  depth: {
    id: 'depth',
    name: 'Depth',
    colors: {
      background: 'linear-gradient(135deg, #000428 0%, #004e92 100%)',
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.8)',
      accent: '#00bfff',
      clouds: 'rgba(0, 4, 40, 0.6)',
      rain: 'rgba(0, 191, 255, 1)',
      snow: 'rgba(255, 255, 255, 1)',
      sun: 'rgba(255, 215, 0, 1)',
      bolt: 'rgba(0, 255, 255, 1)'
    },
    particles: {
      count: 110,
      size: 0.025,
      speed: 0.08
    }
  },

  graphite: {
    id: 'graphite',
    name: 'Graphite',
    colors: {
      background: 'linear-gradient(135deg, #2C3E50 0%, #34495E 50%, #5D6D7E 100%)',
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.8)',
      accent: '#BDC3C7',
      clouds: 'rgba(44, 62, 80, 0.5)',
      rain: 'rgba(52, 73, 94, 1)',
      snow: 'rgba(236, 240, 241, 1)',
      sun: 'rgba(241, 196, 15, 1)',
      bolt: 'rgba(155, 89, 182, 1)'
    },
    particles: {
      count: 85,
      size: 0.02,
      speed: 0.1
    }
  },

  karat: {
    id: 'karat',
    name: 'Karat',
    colors: {
      background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%)',
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.9)',
      accent: '#FFFF00',
      clouds: 'rgba(255, 215, 0, 0.4)',
      rain: 'rgba(30, 144, 255, 1)',
      snow: 'rgba(255, 255, 255, 1)',
      sun: 'rgba(255, 215, 0, 1)',
      bolt: 'rgba(255, 255, 0, 1)'
    },
    particles: {
      count: 75,
      size: 0.03,
      speed: 0.07
    }
  }
};

// Weather condition mappings
export const weatherConditions = {
  // Clear conditions
  'clear': { icon: '☀️', particles: 'sun' },
  'sunny': { icon: '☀️', particles: 'sun' },
  
  // Cloudy conditions
  'clouds': { icon: '☁️', particles: 'clouds' },
  'cloudy': { icon: '☁️', particles: 'clouds' },
  'overcast': { icon: '☁️', particles: 'clouds' },
  'partly-cloudy': { icon: '⛅', particles: 'clouds' },
  
  // Rain conditions
  'rain': { icon: '🌧️', particles: 'rain' },
  'drizzle': { icon: '🌦️', particles: 'rain' },
  'showers': { icon: '🌦️', particles: 'rain' },
  'thunderstorm': { icon: '⛈️', particles: 'storm' },
  
  // Snow conditions
  'snow': { icon: '❄️', particles: 'snow' },
  'sleet': { icon: '🌨️', particles: 'snow' },
  'blizzard': { icon: '❄️', particles: 'snow' },
  
  // Other conditions
  'fog': { icon: '🌫️', particles: 'fog' },
  'mist': { icon: '🌫️', particles: 'fog' },
  'haze': { icon: '🌫️', particles: 'fog' },
  'wind': { icon: '💨', particles: 'wind' }
};

export default weatherThemes;
