"active_membership_found" = "We found an active membership.";
"all_the_skins" = "ALL\nTHE SKINS";
"andy_description" = "THE MATERIAL OF WORK. LIKE EXCAVATORS & POWER TOOLS, THIS IS PAINTED IN BRIGHT, INDUSTRIAL YELLOW.";
"apps" = "APPS";
"apps_and_extras" = "5 APPS + EXTRAS";
"apps_use_every_day" = "5 FRESH\nDAILY APPS";
"app_icon" = "APP ICON";
"app_names_paywall" = "Vibes, Weather, Habits, Timer, Calculator";
"app_name_calculator" = "!Calculator";
"app_name_habits" = "!Habits";
"app_name_timer" = "!Timer";
"app_name_vibes" = "!Vibes";
"app_name_weather" = "!Weather";
"app_store_unavailable" = "THE APP STORE IS NOT AVAILABLE";
"boring_work_shirt" = "!BORING WORK SHIRT!";
"bricks_description" = "THE BASIC COLORFUL BRICKS WHERE BUILDING BEGAN.";
"browse_support" = "Browse Support";
"cancel" = "Cancel";
"cedar_description" = "ARTIST, ERAN HILLELI, CREATES A MYSTICAL ATMOSPHERE WITH DECIMATED FORMS AND LUSCIOUS GREENS. WORKS IN ALL APPS.";
"check_out_answers" = "Check out our answers to common issues regarding subscriptions and more. If you can't find what you are looking for, send us an email.";
"chroma_description" = "BAKE IN THE RAYS OF THIS VINTAGE VIBE. WORKS IN ALL APPS.";
"contact_support" = "Contact Support";
"continue" = "CONTINUE";
"continue_not_capitalized" = "Continue";
"copy_diagnostics" = "Copy Diagnostics to Clipboard";
"customize_look" = "CUSTOMIZE YOUR LOOK";
"depth_description" = "SOFT SHADES OF GRAY REVEAL THE SPACE FILLING THE SCENE.";
"device_cannot_purchase" = "This device is not setup to purchase apps from the Apple App Store";
"five_apps" = "5 APPS";
"follow_on_twitter" = "FOLLOW ON TWITTER";
"footer_not_boring_company" = "THE !BORING SOFTWARE CORPORATION MAKES FUN SOFTWARE FOR LIFE'S BORING MOMENTS.";
"get_it" = "GET IT";
"get_newsletter" = "GET NEWSLETTER";
"grab_item" = "THE SHIRT TO GET THINGS DONE.\nDISCOUNT FOR SUPER!BORING SUPPORTERS";
"graphite_description" = "THE MATERIAL OF CREATION. THE ELEMENTAL FOUNDATION OF LIFE AND THE CORE OF A PENCIL USED TO CAPTURE THE MOMENT AN IDEA TAKES SHAPE.";
"home_lock" = "HOME & LOCK SCREEN";
"independent_creators" = "INDEPENDENT CREATORS";
"insert_coin" = "INSERT\nCOIN";
"karat_description" = "GEOMETRIC FORMS GILDED IN 24 KARATS OF RICH, YELLOW GOLD. WORKS IN ALL APPS.";
"learn_more_andyworks" = "VISIT WEBSITE";
"lifetime_duration" = "LIFETIME";
"manage_subscriptions" = "MANAGE SUBSCRIPTIONS";
"membership" = "MEMBERSHIP";
"membership_found" = "Membership Found!";
"member_supported" = "100% MEMBER SUPPORTED";
"monster_description" = "PLAYFUL BEASTS WITH PEERING EYES & TERRIBLE TEETH. A SECRET IN THE APP WILL UNLOCK ME.";
"monthly" = "MONTHLY";
"new" = "NEW";
"normal_description" = "BLENDING OF RGB VALUES REVEAL THE CONTOURS SHAPING THE SCENE.";
"no_ads_feature" = "NO ADS";
"no_membership_found" = "No active membership was found. Make sure you refresh from the Andy app where you purchased your subscription.";
"no_subscription_found" = "No Membership Found";
"ok" = "OK";
"opal_description" = "THE MATERIAL OF INSPIRATION. FEED YOUR CREATIVE WONDER WITH THIS BEAUTIFUL IRIDESCENT MATERIAL THAT SHIFTS COLORS AS YOU SPIN IT. WORKS IN ALL APPS.";
"paywall_boring_apps" = "boring apps.";
"paywall_less_routine" = "Less routine.";
"paywall_more_play" = "More play.";
"paywall_noads" = "No ads";
"paywall_no_more" = "No more";
"paywall_skins" = "Skins";
"paywall_super_more" = "Widgets, Skins & More";
"paywall_support" = "Support Indie";
"paywall_wallpapers" = "Wallpapers";
"paywall_widgets" = "Widgets";
"presstube_description" = "DIGITAL ARTIST, PRESSTUBE, GRAFFITIS YOUR DAILY ROUTINE WITH HIS SIGNATURE MEDITATIVE SKETCHES. WORKS IN ALL APPS.";
"private" = "PRIVATE";
"purchase_failed_2" = "The purchase failed, please try again.";
"purchase_interrupted_2" = "If your purchase was interrupted, just tap the restore button to complete your purchase.";
"rate_app" = "RATE APP";
"restore" = "RESTORE";
"restore_from_purchased_app" = "You must restore from the app used to purchase the membership.";
"restore_purchases" = "RESTORE PURCHASES";
"see_all_plans" = "SEE ALL PLANS ▲";
"see_plan_details" = "SEE PLAN DETAILS";
"send_email" = "Send Email";
"shop" = "SHOP";
"shop_discount" = "SHOP DISCOUNT";
"show_me" = "SHOW ME";
"single_app_only" = "%@ ONLY";
"skins" = "SKINS";
"skin_artist" = "ARTIST";
"skin_bricks_notification_title" = "New Skin, Bricks!";
"skin_bricks_notification_description" = "A special gift to members.";
"skin_common" = "COMMON";
"skin_hidden_subTitle" = "COMING";
"skin_legendary" = "LEGENDARY";
"skin_rare" = "RARE";
"skin_redeem_description" = "Nice work! You earned it.";
"skin_secret" = "SECRET";
"skin_unlocked" = "%@ Skin Unlocked!";
"subscription_save_yearly" = "(SAVE %1$d%%)";
"success" = "Success";
"super" = "SUPER";
"super_boring" = "SUPER!BORING";
"super_promo_bf_description" = "Our biggest deal ever";
"super_promo_bf_title_line1" = "Black Friday";
"super_promo_bf_title_line2" = "Sale!";
"super_promo_bf_notification_title" = "Black Friday Sale!";
"super_promo_description" = "5 APPS TO USE EVERY DAY\n+ SKINS, WIDGETS, & MORE";
"support" = "SUPPORT";
"support_indie" = "SUPPORT\nCREATORS";
"support_lowercase" = "Support";
"tap_for_more" = "TAP FOR MORE";
"tap_to" = "TAP TO";
"tap_to_continue" = "TAP TO CONTINUE";
"tap_to_retry" = "TAP TO RETRY";
"tap_to_unlock" = "TAP TO UNLOCK";
"terms_and_privacy" = "TERMS & PRIVACY";
"thomas_music" = "MUSIC BY THOMAS WILLIAMS";
"try_free" = "TRY FREE FOR %d DAYS";
"uh_oh_not_capitalized" = "Uh Oh!";
"upgrade_existing_subscription" = "Upgrade an Existing Subscription";
"upgrade_super_detected" = "We've detected an existing subscription in another app. Please upgrade to Super!Boring via the %@ app.";
"view_subscriptions" = "View Subscriptions";
"wallpapers" = "WALLPAPERS";
"wearing" = "WEARING";
"wear_it" = "WEAR IT";
"welcome_to_club" = "WELCOME TO THE CLUB!";
"widgets" = "WIDGETS";
"widgets_wallpapers" = "WIDGETS &\nWALLPAPERS";
"wireframe_description" = "VIBRANT EDGES REVEAL THE HIDDEN STRUCTURES SUPPORTING THE SCENE.";
"work_shirt_002_description" = "The shirt to get things done.\nDiscount for Super!Boring members";
"work_shirt_002_unlocked" = "Work Shirt 3\nUnlocked!";
"yearly" = "YEARLY";
"zero_data" = "ZERO DATA & TRACKING";
