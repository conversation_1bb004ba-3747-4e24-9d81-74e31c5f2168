<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <canvas id="canvas192" width="192" height="192"></canvas>
    <canvas id="canvas512" width="512" height="512"></canvas>
    <br>
    <a id="download192" download="icon-192.png">Download 192x192</a>
    <br>
    <a id="download512" download="icon-512.png">Download 512x512</a>

    <script>
        function generateIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // Draw rounded rectangle background
            const radius = size * 0.2;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // Draw sun
            const sunX = size * 0.62;
            const sunY = size * 0.37;
            const sunRadius = size * 0.12;
            
            ctx.fillStyle = '#FFD700';
            ctx.beginPath();
            ctx.arc(sunX, sunY, sunRadius, 0, Math.PI * 2);
            ctx.fill();
            
            // Draw cloud
            const cloudY = size * 0.62;
            const cloudHeight = size * 0.25;
            const cloudWidth = size * 0.56;
            const cloudX = size * 0.31;
            
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            
            // Main cloud body
            ctx.beginPath();
            ctx.roundRect(cloudX, cloudY, cloudWidth, cloudHeight * 0.6, cloudHeight * 0.3);
            ctx.fill();
            
            // Cloud puffs
            const puffRadius = cloudHeight * 0.4;
            ctx.beginPath();
            ctx.arc(cloudX + puffRadius, cloudY + puffRadius * 0.5, puffRadius, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(cloudX + cloudWidth - puffRadius, cloudY + puffRadius * 0.5, puffRadius, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(cloudX + cloudWidth * 0.5, cloudY, puffRadius * 0.8, 0, Math.PI * 2);
            ctx.fill();
        }

        // Generate both icons
        generateIcon(document.getElementById('canvas192'), 192);
        generateIcon(document.getElementById('canvas512'), 512);
        
        // Set up download links
        document.getElementById('download192').href = document.getElementById('canvas192').toDataURL();
        document.getElementById('download512').href = document.getElementById('canvas512').toDataURL();
    </script>
</body>
</html>
