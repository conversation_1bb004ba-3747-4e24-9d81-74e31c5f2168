import React, { useState } from 'react';
import '../styles/InstallPrompt.css';

const InstallPrompt = ({ onInstall, onDismiss }) => {
  const [isVisible, setIsVisible] = useState(true);

  const handleInstall = () => {
    onInstall();
    setIsVisible(false);
  };

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss();
  };

  if (!isVisible) return null;

  return (
    <div className="install-prompt-overlay">
      <div className="install-prompt">
        <div className="install-prompt-content">
          <div className="install-icon">📱</div>
          <h3>Install Simple Weather</h3>
          <p>Add to your home screen for quick access to beautiful weather forecasts</p>
          
          <div className="install-features">
            <div className="feature">
              <span className="feature-icon">⚡</span>
              <span>Instant loading</span>
            </div>
            <div className="feature">
              <span className="feature-icon">📱</span>
              <span>Works offline</span>
            </div>
            <div className="feature">
              <span className="feature-icon">🎨</span>
              <span>Beautiful 3D effects</span>
            </div>
          </div>
        </div>
        
        <div className="install-actions">
          <button className="install-button" onClick={handleInstall}>
            Install App
          </button>
          <button className="dismiss-button" onClick={handleDismiss}>
            Not Now
          </button>
        </div>
      </div>
    </div>
  );
};

export default InstallPrompt;
