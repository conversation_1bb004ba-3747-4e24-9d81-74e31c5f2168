.install-prompt-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.install-prompt {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 32px 24px 24px;
  max-width: 360px;
  width: 90%;
  text-align: center;
  color: white;
  animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.install-prompt-content {
  margin-bottom: 24px;
}

.install-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.install-prompt h3 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
}

.install-prompt p {
  font-size: 16px;
  opacity: 0.9;
  line-height: 1.5;
  margin-bottom: 24px;
  font-weight: 300;
}

.install-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 8px;
}

.feature {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 14px;
  opacity: 0.9;
}

.feature-icon {
  font-size: 16px;
}

.install-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.install-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 16px;
  padding: 16px 24px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
}

.install-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.install-button:active {
  transform: translateY(0);
}

.dismiss-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 14px 24px;
  color: white;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
}

.dismiss-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Mobile responsive */
@media (max-width: 480px) {
  .install-prompt {
    padding: 28px 20px 20px;
    border-radius: 20px;
  }
  
  .install-icon {
    font-size: 40px;
    margin-bottom: 12px;
  }
  
  .install-prompt h3 {
    font-size: 20px;
    margin-bottom: 8px;
  }
  
  .install-prompt p {
    font-size: 14px;
    margin-bottom: 20px;
  }
  
  .install-features {
    gap: 10px;
    margin-bottom: 4px;
  }
  
  .feature {
    font-size: 13px;
  }
  
  .install-button {
    padding: 14px 20px;
    font-size: 15px;
  }
  
  .dismiss-button {
    padding: 12px 20px;
    font-size: 13px;
  }
}
