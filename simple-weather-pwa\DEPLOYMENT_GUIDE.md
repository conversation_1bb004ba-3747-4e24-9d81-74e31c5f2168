# 🚀 GitHub Pages Deployment Guide

## 📍 Your Deployment URL
**Live Site**: https://yisroel-w.github.io/simple-weather-pwa/

## ⚠️ Troubleshooting 404 Error

If you're seeing a 404 error, here's how to fix it:

### **Step 1: Enable GitHub Pages**
1. Go to your repository: https://github.com/yisroel-w/simple-weather-pwa
2. Click **Settings** tab
3. Scroll down to **Pages** section (left sidebar)
4. Under **Source**, select **"GitHub Actions"**
5. Save the settings

### **Step 2: Check GitHub Actions**
1. Go to **Actions** tab in your repository
2. Look for the "Deploy Simple Weather PWA to GitHub Pages" workflow
3. If it's red (failed), click on it to see the error
4. If it's not running, click **"Run workflow"** manually

### **Step 3: Verify Branch**
Your repository is using `master` branch. The workflow has been updated to support both `main` and `master`.

### **Step 4: Manual Trigger**
If the workflow hasn't run automatically:
1. Go to **Actions** tab
2. Click on **"Deploy Simple Weather PWA to GitHub Pages"**
3. Click **"Run workflow"** button
4. Select `master` branch
5. Click **"Run workflow"**

## 🔧 Alternative: Manual Deployment

If GitHub Actions isn't working, you can deploy manually:

### **Option 1: Build and Deploy Branch**
```bash
# In your local repository
npm run build
git add dist -f
git commit -m "Add build files for GitHub Pages"
git subtree push --prefix dist origin gh-pages
```

Then in GitHub Settings → Pages → Source: select "Deploy from a branch" → `gh-pages` → `/ (root)`

### **Option 2: Simple GitHub Pages**
1. Create a new branch called `gh-pages`
2. Copy all files from `dist/` folder to the root of `gh-pages` branch
3. Push the `gh-pages` branch
4. In Settings → Pages → Source: select "Deploy from a branch" → `gh-pages`

## ✅ Verification Steps

Once deployed, verify these work:

### **Basic Functionality**
- [ ] Site loads without errors
- [ ] Weather data displays (Seattle, WA demo data)
- [ ] 3D scene renders
- [ ] Theme selector works (🎨 button)

### **PWA Features**
- [ ] Install prompt appears
- [ ] App can be installed
- [ ] Works offline
- [ ] Service worker registers

### **Performance**
- [ ] Loads in under 3 seconds
- [ ] 3D animations are smooth
- [ ] Responsive on mobile

## 🐛 Common Issues

### **404 Error**
- GitHub Pages not enabled
- Wrong source branch selected
- Deployment still in progress

### **Blank Page**
- Check browser console for errors
- Verify all assets loaded correctly
- Check if base path is correct in vite.config.js

### **3D Scene Not Working**
- Browser doesn't support WebGL
- Three.js failed to load
- Check console for JavaScript errors

### **PWA Not Installing**
- Site must be served over HTTPS (GitHub Pages does this automatically)
- Service worker must register successfully
- Manifest.json must be valid

## 📞 Need Help?

If you're still having issues:

1. **Check the Actions tab** for build errors
2. **Look at browser console** for JavaScript errors
3. **Verify GitHub Pages settings** are correct
4. **Try the manual deployment** option above

## 🎯 Expected Timeline

- **GitHub Actions**: 2-5 minutes after push
- **GitHub Pages**: Additional 1-2 minutes to propagate
- **Total**: 3-7 minutes from push to live site

## 📱 Testing Your Deployment

Once live, test these features:

1. **Open**: https://yisroel-w.github.io/simple-weather-pwa/
2. **Install**: Look for browser install prompt
3. **Offline**: Disconnect internet, reload page
4. **Mobile**: Test on phone/tablet
5. **Themes**: Try different weather themes
6. **Performance**: Check load speed and animations

Your Simple Weather PWA should be fully functional with beautiful 3D weather effects! 🌤️
