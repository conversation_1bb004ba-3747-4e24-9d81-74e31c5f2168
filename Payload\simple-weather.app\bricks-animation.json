{
  "animations": {
    "sunIn": {
      "actions": [
        {
          "nativeAnimation": "planeColor",
          "targetColor": "0 182 54 255"
        },
        {
          "nativeAnimation": "planeNoiseIntensity",
          "intensity": 0.0
        },
        {
          "nativeAnimation": "fogColor",
          "targetColor": "0 182 54 255"
        },
        {
          "nativeAnimation": "statusBarStyle",
          "statusBarStyle": "dark"
        }
      ]
    },
    "moonIn": {
      "actions": [
        {
          "nativeAnimation": "planeColor",
          "targetColor": "0 43 110 255"
        },
        {
          "nativeAnimation": "planeNoiseIntensity",
          "intensity": 0.0
        },
        {
          "nativeAnimation": "fogColor",
          "targetColor": "0 43 110 255"
        },
        {
          "nativeAnimation": "statusBarStyle",
          "statusBarStyle": "light"
        }
      ]
    },
  }
}
