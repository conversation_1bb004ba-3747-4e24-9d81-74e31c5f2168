<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Assets.car</key>
		<data>
		2KnS9FqtJMxN/zDdXi5HWwC4s9k=
		</data>
		<key>FoundersGrotesk-Bold.otf</key>
		<data>
		2onx4TVf2oQUN/N+kWXCX1bMBVM=
		</data>
		<key>FoundersGrotesk-BoldItalic.otf</key>
		<data>
		CX5j2Du1XPDvIS+rxvRbLIFobr0=
		</data>
		<key>FoundersGrotesk-Regular.otf</key>
		<data>
		3BnSp4IubDZkP8RBbw1SrpzBg6o=
		</data>
		<key>FoundersGrotesk-RegularItalic.otf</key>
		<data>
		QDUI6uUJj6osKw42qy7fb2XZE3g=
		</data>
		<key>Info.plist</key>
		<data>
		nDvSjMXwivEwMX/EUMsSruR2iHc=
		</data>
		<key>JetBrainsMono-Regular.ttf</key>
		<data>
		1j47G+nzKSg93KFFVPTMf5qjKJw=
		</data>
		<key>NeueMaticCompressed-Bold.ttf</key>
		<data>
		2k9mO0xPIw+HDws6/dnu1Bhzfug=
		</data>
		<key>NeumaticGothic-Bold.ttf</key>
		<data>
		EYsj/rWvyLxnVzQKDxB09HVSmKE=
		</data>
		<key>NeumaticGothic-Regular.ttf</key>
		<data>
		Zqsr2ZsfnhaBOC7iTrsFx5xImvI=
		</data>
		<key>RevenueCat_RevenueCat.bundle/Info.plist</key>
		<data>
		MVZ+U0wtJQF7b0Wc1vq1FzkY86I=
		</data>
		<key>RevenueCat_RevenueCat.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		uqRDkW3s33psRj8H+jal4YSzsvk=
		</data>
		<key>RevenueCat_RevenueCatUI.bundle/Assets.car</key>
		<data>
		VkivHEN0Sq7H7WpXWg2/23RU0nY=
		</data>
		<key>RevenueCat_RevenueCatUI.bundle/Info.plist</key>
		<data>
		1t40HiElje0wTOPeNb3VuynXqd8=
		</data>
		<key>RevenueCat_RevenueCatUI.bundle/ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IgrDYE7s5Ao1kgXGWhkmE6DtaSw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/background.jpg</key>
		<data>
		cobmQyDEiwBkOm53urv6Z0Jheeg=
		</data>
		<key>RevenueCat_RevenueCatUI.bundle/bg.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1DK4kp//KoPu6EliBCoHJSm3hSg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+0WdXT9iA7rxmekkeFV1CFfUr0M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzfk/vzfrNFlQAV78mNv+sM+cLs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/da.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Tb1M+JlXwha6CASNZN99jg1lCjY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+1wNXbdjC5n5rXHD3ujvnjU2aOw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/el.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			j7zTilv4aawn1JvdeWDti6lfvpY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_AU.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_CA.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_GB.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_US.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/es_419.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rp8pvRolRrgVJOqfZRqqPqT/zB0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/es_ES.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rp8pvRolRrgVJOqfZRqqPqT/zB0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zPSCYzm5sUbOkWRJLBpML1c7bpI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/fr_CA.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fwtMweVYC4tNUSn94QdCECWfosc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/he.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OOwIfBI8gTKKCd6IG0lAxSVfGHI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/hi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XoLnoVnrb8rYzqZkbWn4Y4jmyoM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/hr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v69QiECFoEXKsPyKgwTM6/0+988=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7U3zm8yKWi31CkYV5toBCx2pHfA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/id.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dTay5oBh44GUzpUfM/MYxpycpZk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oq1WnovzEqv8jMG/r3GzZdYL9bg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mhl5cJsbiu8E630s+IBtqg/rGS8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/kk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p+pJpe7nUaOnu2TSmYQ/7On9YrI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xMJ1BrfbetANxc5M2m4JLDeGvBY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ms.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GJ013SGsBV29jWSP6lMoiNKrlsE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rj/1cU0FueMctW5Idlc4DffFL4A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/no.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ohd0nuuHYRbQH5m7Iav1VkCBIhE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Y7VJO0AodP4+HUtD99S53Fo1eE0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/pt_BR.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ouomTBJZPP2rz7ePbMzBtD4zVCk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/pt_PT.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ouomTBJZPP2rz7ePbMzBtD4zVCk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XdJv/J/wOKxJMc4m1bBz6QiG8xA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gwj1mlzWt5aADE1iE+9KDdkAzhw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TfCNaaj825wCwvrXLJ8bozt6Mxo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5stHn6nvQv0kKmzer+shwoxHgVw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/th.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8fWP6ZGNpnRL2mozleLAHZu+asE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AyHk7CPwbQ/0lK4wnv+2YTBsYmE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/uk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uXBx8YT+kSTOMpZhEST69s90wIc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/vi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9b8184pr3yNjMqWMKU8oyGTuGjE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/zh_Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XfpxFjPoJ1mnulmHM595TUxkDfs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/zh_Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0DPdeshPj2dIZd5BIzKTZhyQmgg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/Assets.car</key>
		<data>
		QuY1SRVaDhGUKRnTDG7pGEGtTGk=
		</data>
		<key>SimpleCore_SimpleCore.bundle/Info.plist</key>
		<data>
		icomyb6ys7NNbVOtazV8NyG9qCo=
		</data>
		<key>SimpleCore_SimpleCore.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zzQ2NXhAgc2EwqNSrghGyx75Zsk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CbHT+zxH4kLofQyjzZqOn9U/XuU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PEPEDUKRjYVTc9rZmKejmxuAu3M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/explode-open.ahap</key>
		<data>
		VAAR35KfKPUX5E+tFwedxZlNJUo=
		</data>
		<key>SimpleCore_SimpleCore.bundle/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qwFbqEe7kyM+4s62SMsPOhNdEUo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qSuDCVKNGwFjGLeFtsK9eMCV4a8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7HVCabV9EkQSS02uDL+rXCxyXOY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i91b5xMeECm4CpmeJQpIHYXuM9Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PWYvfp57Fjn115z1QgQ2u3WXXd0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Kz6CRitjor6eDbhbEEqNpb5J7rE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>andy-animation.json</key>
		<data>
		4jcmNT2NgfK5sDTc6WBk14nGqiU=
		</data>
		<key>andy-colors.json</key>
		<data>
		1q9gV9plEJkY1/VBOwPlOEpsbzQ=
		</data>
		<key>andy-theme.json</key>
		<data>
		Y+w8FBhrkhcbFJtP937+cb3eGyQ=
		</data>
		<key>base1-animation.json</key>
		<data>
		JkEC572cv5WTKSHKxpNuo82gNYI=
		</data>
		<key>bricks-animation.json</key>
		<data>
		P1+SaemHjcyujmgUm1t/FwGxYWk=
		</data>
		<key>bricks-colors.json</key>
		<data>
		nyzxG2TyHd5NtldIqNnUQM/aon8=
		</data>
		<key>bricks-theme.json</key>
		<data>
		VzPY0XwcukSTzgAF+CiU+gZsnxw=
		</data>
		<key>cedar-animation.json</key>
		<data>
		Jd2PtxGD1MmcLk0cGgW9I9RpN4w=
		</data>
		<key>cedar-colors.json</key>
		<data>
		nAOcGGMgqJqL5iNos2AutWeV3nc=
		</data>
		<key>cedar-theme.json</key>
		<data>
		aHOIurzBqkzl3ksBfe8gS5fi1d8=
		</data>
		<key>chroma-animation.json</key>
		<data>
		RUupW9F8QlvVdsAGvZm4Q+D20Ko=
		</data>
		<key>chroma-colors.json</key>
		<data>
		dtx1Kc3YLWxPaHUsznENRU9tLcQ=
		</data>
		<key>chroma-theme.json</key>
		<data>
		stGwudiXtOLqJl1VTczFzTGE+ZU=
		</data>
		<key>de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LTDON6YEF7ST70ahhzpRlX+dllo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>depth-animation.json</key>
		<data>
		i6AHSjdO9nTgEpGKiYpKnCwd47E=
		</data>
		<key>depth-colors.json</key>
		<data>
		PqAdyFaK6bIPty7YiB1cXxv7owc=
		</data>
		<key>depth-theme.json</key>
		<data>
		yZ1JyHEvjpSoiYQc9jN7FD+49CA=
		</data>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p+nwJG17/r4UQWIvuyAQPZDS7uM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/EHBdA5VdycM64mhhD4lJJsUVP4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mSmC+D9j02gAPc1cwywbssQiBKQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cVBkX8o0wGEzHC1mqOzEvPkmlLU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>graphite-animation.json</key>
		<data>
		YR7wQ7O/RhHvqBigzewKV/syQZQ=
		</data>
		<key>graphite-colors.json</key>
		<data>
		np7EJs3SHyGXbeA+fX/cfEzy480=
		</data>
		<key>graphite-theme.json</key>
		<data>
		LqeVMAoHG+oCR5NyV/+9Hz4QJBM=
		</data>
		<key>it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2P+PjoufHmOEDNu336ve6rqLDAM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nELECNjh/DXfP4ElOEfZe7X4XIA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>karat-animation.json</key>
		<data>
		gAGkywTUQ2hFODLtmtC7gEwXP3c=
		</data>
		<key>karat-colors.json</key>
		<data>
		+Mq70YusyQAn+9A4Y1D2F4AMigs=
		</data>
		<key>karat-theme.json</key>
		<data>
		ys3gO/H606017EQix6ZrYbu97yA=
		</data>
		<key>ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/wuFqBA3/wO0M0tKH1oW+Qlir9Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>monster-animation.json</key>
		<data>
		aEtpqqeq25BOAsESeZjUYMwK1YI=
		</data>
		<key>monster-colors.json</key>
		<data>
		NItWhVRF0e5NKx61tLhQsS+JVgs=
		</data>
		<key>monster-theme.json</key>
		<data>
		OTuCV/89NlfbPugIWset1pcmTAk=
		</data>
		<key>normal-animation.json</key>
		<data>
		cf3RwXmcWshBcK2KLXyob6vO1j0=
		</data>
		<key>normal-colors.json</key>
		<data>
		8YiWkSvW0zx9OVrCiilifY6iCCQ=
		</data>
		<key>normal-theme.json</key>
		<data>
		E7YQDPKEN6UPLK8vqrW7ynr5N6U=
		</data>
		<key>opal-animation.json</key>
		<data>
		JxA+QKOggC+LfCpsyltQYX8TGX8=
		</data>
		<key>opal-colors.json</key>
		<data>
		lRY8D0TWIc8/rI6FvomnxAdGv/w=
		</data>
		<key>opal-theme.json</key>
		<data>
		W8U9oQe4l5m28/gHjL8ZjtVvpuc=
		</data>
		<key>presstube-animation.json</key>
		<data>
		Jh/2brca/CFAkquNIrJuhQdDC9g=
		</data>
		<key>presstube-colors.json</key>
		<data>
		hRGtshy6vCyOXnkqMXig+f4hvY8=
		</data>
		<key>presstube-theme.json</key>
		<data>
		P8bZldX26ircgfJIKVyDMzuYeK8=
		</data>
		<key>wireframe-animation.json</key>
		<data>
		oPf7NGvKfp9A/y1DmScXTxiRbzs=
		</data>
		<key>wireframe-colors.json</key>
		<data>
		SkET4REpucuQlvEopWhZgzy3xvY=
		</data>
		<key>wireframe-theme.json</key>
		<data>
		8ji1OdI/yl3KdcG520CtnE7BUCE=
		</data>
		<key>zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HGf0fK/p7N26ga/Y9qcO9p63+AA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Shi/GjyOXaU+CsI6mdntDrdGAW0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+ciEm+qDGp+UZoYEmWF/ETcdQhA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			2KnS9FqtJMxN/zDdXi5HWwC4s9k=
			</data>
			<key>hash2</key>
			<data>
			46xoAsj/iv7Ba+6RMnuIxyatbhyYH4geJuLcUaxTLiU=
			</data>
		</dict>
		<key>FoundersGrotesk-Bold.otf</key>
		<dict>
			<key>hash</key>
			<data>
			2onx4TVf2oQUN/N+kWXCX1bMBVM=
			</data>
			<key>hash2</key>
			<data>
			8whaOo1h3QD+5t1hjpoFPWQ8c2QJeBlVgzhiNpQ3/uQ=
			</data>
		</dict>
		<key>FoundersGrotesk-BoldItalic.otf</key>
		<dict>
			<key>hash</key>
			<data>
			CX5j2Du1XPDvIS+rxvRbLIFobr0=
			</data>
			<key>hash2</key>
			<data>
			N3B/INt6V83HoShY7xxhcOPfHgD5ofUXbR7niB6bKFY=
			</data>
		</dict>
		<key>FoundersGrotesk-Regular.otf</key>
		<dict>
			<key>hash</key>
			<data>
			3BnSp4IubDZkP8RBbw1SrpzBg6o=
			</data>
			<key>hash2</key>
			<data>
			HbybZigZsQvvEBa1t+U6ZlbYX2zqKafvO4dB0x9n59I=
			</data>
		</dict>
		<key>FoundersGrotesk-RegularItalic.otf</key>
		<dict>
			<key>hash</key>
			<data>
			QDUI6uUJj6osKw42qy7fb2XZE3g=
			</data>
			<key>hash2</key>
			<data>
			Q/qHHO2IwxAsspM5/tgEdd0bEEKwF27q7W1ZmOUtPN4=
			</data>
		</dict>
		<key>JetBrainsMono-Regular.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			1j47G+nzKSg93KFFVPTMf5qjKJw=
			</data>
			<key>hash2</key>
			<data>
			pL1p0JPssbXsC9zBQDcETds//jy5o7zn5Z9zikXDkT8=
			</data>
		</dict>
		<key>NeueMaticCompressed-Bold.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			2k9mO0xPIw+HDws6/dnu1Bhzfug=
			</data>
			<key>hash2</key>
			<data>
			tKSBMrwQmMa59DPRcv2Q4Fel/RwQJiRFuyRip7+l/RQ=
			</data>
		</dict>
		<key>NeumaticGothic-Bold.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			EYsj/rWvyLxnVzQKDxB09HVSmKE=
			</data>
			<key>hash2</key>
			<data>
			I7WJ9ZyM2nj+t4vRoC39+rh/hAv/gOCr7srj2UP0bnQ=
			</data>
		</dict>
		<key>NeumaticGothic-Regular.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			Zqsr2ZsfnhaBOC7iTrsFx5xImvI=
			</data>
			<key>hash2</key>
			<data>
			YoyEqh0BgbyFNwNYfIFaD3PFiWVRTFsSwFzlnODwG+A=
			</data>
		</dict>
		<key>RevenueCat_RevenueCat.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			MVZ+U0wtJQF7b0Wc1vq1FzkY86I=
			</data>
			<key>hash2</key>
			<data>
			xQsZsiJSGt/IsaDYQFLvLHr96Tv14ZYkUXX62B2dlIE=
			</data>
		</dict>
		<key>RevenueCat_RevenueCat.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			uqRDkW3s33psRj8H+jal4YSzsvk=
			</data>
			<key>hash2</key>
			<data>
			dsh2xzvzfWOylE0EkeTZpD1CDeeLZlEvzKJ6SErpLnk=
			</data>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			VkivHEN0Sq7H7WpXWg2/23RU0nY=
			</data>
			<key>hash2</key>
			<data>
			Tz5m+nyD7kNfykbGT2vBINUZrSTefdkeDK0zn3r28Sk=
			</data>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			1t40HiElje0wTOPeNb3VuynXqd8=
			</data>
			<key>hash2</key>
			<data>
			zxmWYFxM1nEJ+FztgzwpU3W2as5syAUkahW37jJ1pDM=
			</data>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IgrDYE7s5Ao1kgXGWhkmE6DtaSw=
			</data>
			<key>hash2</key>
			<data>
			43XLVDTng+RxO2Nl9fMmH+c/h9SHao9PktitA/Zzv0I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/background.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			cobmQyDEiwBkOm53urv6Z0Jheeg=
			</data>
			<key>hash2</key>
			<data>
			dUOKLLcnrAPN0z7XnuVdMdCw1n1XpS67wiyRMR9CCtE=
			</data>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/bg.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1DK4kp//KoPu6EliBCoHJSm3hSg=
			</data>
			<key>hash2</key>
			<data>
			UcYGOuNA78F50VFNATTgdND4sFlqtw1fA3jB4C0giU8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+0WdXT9iA7rxmekkeFV1CFfUr0M=
			</data>
			<key>hash2</key>
			<data>
			b5oSqB1JdHECbRfm3S/1tZLC0Lh1+KdHY7vGtyYMqQ4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzfk/vzfrNFlQAV78mNv+sM+cLs=
			</data>
			<key>hash2</key>
			<data>
			gg0Ziev9GuMAHbm+irLQSQxKT9nUMKs97An7FiRKUK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/da.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Tb1M+JlXwha6CASNZN99jg1lCjY=
			</data>
			<key>hash2</key>
			<data>
			OVGMstuHYWej4jOlPLROEtWw8XqPVvrvgmcrsifmod4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+1wNXbdjC5n5rXHD3ujvnjU2aOw=
			</data>
			<key>hash2</key>
			<data>
			oODtsRpPj+kqJY/iEx87zD+zNZSWC8IED5HsdfQ7Grk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/el.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			j7zTilv4aawn1JvdeWDti6lfvpY=
			</data>
			<key>hash2</key>
			<data>
			0HHzcsA0JHJCagOM9prG8NTF2yRKYl+6RSHHONYyqkU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_AU.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_CA.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_GB.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_US.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/es_419.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rp8pvRolRrgVJOqfZRqqPqT/zB0=
			</data>
			<key>hash2</key>
			<data>
			pDdqBQdRB/UK2nCPJdYqYziGEIvo4Cba0u/5rfkiGC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/es_ES.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rp8pvRolRrgVJOqfZRqqPqT/zB0=
			</data>
			<key>hash2</key>
			<data>
			pDdqBQdRB/UK2nCPJdYqYziGEIvo4Cba0u/5rfkiGC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zPSCYzm5sUbOkWRJLBpML1c7bpI=
			</data>
			<key>hash2</key>
			<data>
			CoJxRV6Pa4Nd7I97UYLcv4Jrek4GcFWK55nF7w1219Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/fr_CA.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fwtMweVYC4tNUSn94QdCECWfosc=
			</data>
			<key>hash2</key>
			<data>
			ESlXxVU7u3wwc8kV7U8m5LZ1geYWZKxspFO3Oe3RDqc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/he.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OOwIfBI8gTKKCd6IG0lAxSVfGHI=
			</data>
			<key>hash2</key>
			<data>
			KXx+u6IQWoTr824w+fPwy4RA0BmZ7rLNPswCkgu0cJk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/hi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XoLnoVnrb8rYzqZkbWn4Y4jmyoM=
			</data>
			<key>hash2</key>
			<data>
			9MU3l/uznWBioc45/UaIxB1dylNR9IwQCoGTo9pWNiM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/hr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v69QiECFoEXKsPyKgwTM6/0+988=
			</data>
			<key>hash2</key>
			<data>
			e6VV5rAcviIgcspYOB6adkf5jkMXJJCoPGqjIhuL3Kk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7U3zm8yKWi31CkYV5toBCx2pHfA=
			</data>
			<key>hash2</key>
			<data>
			xshQSdY9UAEi26wunbyNoa/T4QDen+KhIBk2C21ILp0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/id.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dTay5oBh44GUzpUfM/MYxpycpZk=
			</data>
			<key>hash2</key>
			<data>
			j5r/tRvArqey/5v9T8qd1zpX13DqqgVxmilibx0Sfjk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oq1WnovzEqv8jMG/r3GzZdYL9bg=
			</data>
			<key>hash2</key>
			<data>
			n+gHBC7biprvjHovhOdEWSzjLrAhlJOJ4CKFVrxTn98=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mhl5cJsbiu8E630s+IBtqg/rGS8=
			</data>
			<key>hash2</key>
			<data>
			gFCMNFYa5kp7SH5wfOkUq2EQM750ytvzgICRVrLVero=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/kk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p+pJpe7nUaOnu2TSmYQ/7On9YrI=
			</data>
			<key>hash2</key>
			<data>
			o3YdmKFaoOR1ws7EFJ8g2ciB1pWAf4zjTvqjRtTWu2k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xMJ1BrfbetANxc5M2m4JLDeGvBY=
			</data>
			<key>hash2</key>
			<data>
			PohLRw2CSHs6uubqf4DFU+Qx1xWAZnE9TV2rqoxyR7o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ms.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GJ013SGsBV29jWSP6lMoiNKrlsE=
			</data>
			<key>hash2</key>
			<data>
			Y8vzlr2gAJfyQLYMUgGXXl1XqcBfum+LNMdVkJcHLQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rj/1cU0FueMctW5Idlc4DffFL4A=
			</data>
			<key>hash2</key>
			<data>
			1pAA+jZTTiofbRsaNq8n5XwtzJCGOzKdqd0tJ3h2NaA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/no.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ohd0nuuHYRbQH5m7Iav1VkCBIhE=
			</data>
			<key>hash2</key>
			<data>
			eFgxLmQmhzdTxLRH4giZ2aO2Mkg0KnIO3ncDn9Vuncg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Y7VJO0AodP4+HUtD99S53Fo1eE0=
			</data>
			<key>hash2</key>
			<data>
			guvT7rsPqhNT4GkxCbQ6lWBtsjFev6qySd8m4jnYDr0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/pt_BR.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ouomTBJZPP2rz7ePbMzBtD4zVCk=
			</data>
			<key>hash2</key>
			<data>
			Z5irvoPF5zXqIqhlnUAVrCeVHTjHqVk4Cfvnard/fQ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/pt_PT.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ouomTBJZPP2rz7ePbMzBtD4zVCk=
			</data>
			<key>hash2</key>
			<data>
			Z5irvoPF5zXqIqhlnUAVrCeVHTjHqVk4Cfvnard/fQ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XdJv/J/wOKxJMc4m1bBz6QiG8xA=
			</data>
			<key>hash2</key>
			<data>
			GtQPLiPA+Uk53qR5drn7D/ncWt2yhBH6ygD3xN25F3M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gwj1mlzWt5aADE1iE+9KDdkAzhw=
			</data>
			<key>hash2</key>
			<data>
			fxWh6lhtonsBacOb7VKKPmXQ6fqla3piCYLscgCVZ5I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TfCNaaj825wCwvrXLJ8bozt6Mxo=
			</data>
			<key>hash2</key>
			<data>
			dVMrrEuRiOMMS1eBLCRxai+LPDaQ7OrYV5zh2zRNBGw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5stHn6nvQv0kKmzer+shwoxHgVw=
			</data>
			<key>hash2</key>
			<data>
			PNcifbuPjOUyFmdDi0+cVhbIKpvGlntSODoqr4YXoSM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/th.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8fWP6ZGNpnRL2mozleLAHZu+asE=
			</data>
			<key>hash2</key>
			<data>
			NpsWECdHUeJrOZfEspsXf4s24BbRRJGYcUQfkrlVHkY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AyHk7CPwbQ/0lK4wnv+2YTBsYmE=
			</data>
			<key>hash2</key>
			<data>
			9jPnfcLSTyByMPQkGNLOlcLAVRV3cmeaRWnt9vKIduU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/uk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uXBx8YT+kSTOMpZhEST69s90wIc=
			</data>
			<key>hash2</key>
			<data>
			da2IURR8kJyt0L3GcWe4zc2p/n8cYHeLq1JD4H17los=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/vi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9b8184pr3yNjMqWMKU8oyGTuGjE=
			</data>
			<key>hash2</key>
			<data>
			iaiXbEUOQKHQpYx2LFR/Uo17Q7HtZi1SuJZIJRrLADQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/zh_Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XfpxFjPoJ1mnulmHM595TUxkDfs=
			</data>
			<key>hash2</key>
			<data>
			B/Cd47WjB1eLekpDRWtOEg8B5XlGB7i8DwzX5xDiSMg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/zh_Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0DPdeshPj2dIZd5BIzKTZhyQmgg=
			</data>
			<key>hash2</key>
			<data>
			DRu8+ryqih5ps2AiFXXdhc+WzTE1PO9rRy54GZof3Qo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			QuY1SRVaDhGUKRnTDG7pGEGtTGk=
			</data>
			<key>hash2</key>
			<data>
			fGnb9cDagxtyZQf+EZMh/pMwvHmd4VG7tVcvRdW+sig=
			</data>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			icomyb6ys7NNbVOtazV8NyG9qCo=
			</data>
			<key>hash2</key>
			<data>
			xGARvdtdNHaznX/CaaxpV5jGbsqDj03cHWFPQCW2tmE=
			</data>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zzQ2NXhAgc2EwqNSrghGyx75Zsk=
			</data>
			<key>hash2</key>
			<data>
			XklvUMEVmikU0nWUS3YR394W5o5s8BCULNiGug7OHWk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CbHT+zxH4kLofQyjzZqOn9U/XuU=
			</data>
			<key>hash2</key>
			<data>
			2NE7okTcdorba2w2FAt2yUbXHRavMhBMgaXP8jzOl6o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PEPEDUKRjYVTc9rZmKejmxuAu3M=
			</data>
			<key>hash2</key>
			<data>
			ZMdyVfEtjdSiwAEeVJV1e0g1cw1POl4bN4qG0vbnIjs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/explode-open.ahap</key>
		<dict>
			<key>hash</key>
			<data>
			VAAR35KfKPUX5E+tFwedxZlNJUo=
			</data>
			<key>hash2</key>
			<data>
			SwGgC+ZQcEOmSzh8XWviqiQWstN8FCM4UzIi6SYtdgw=
			</data>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qwFbqEe7kyM+4s62SMsPOhNdEUo=
			</data>
			<key>hash2</key>
			<data>
			aSfo6FOoUzQq2lleHzFFaMJz+QDKCZG1fLwhjsfiOeU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qSuDCVKNGwFjGLeFtsK9eMCV4a8=
			</data>
			<key>hash2</key>
			<data>
			CoNfGY5mn1c7EpoJWtK/HVu/LU+rL/JYk86ipRbsE5A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7HVCabV9EkQSS02uDL+rXCxyXOY=
			</data>
			<key>hash2</key>
			<data>
			a3+lgEtoRLsvIerzNwiFRFQJOduCQX2BLAtzSqCNgvI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i91b5xMeECm4CpmeJQpIHYXuM9Q=
			</data>
			<key>hash2</key>
			<data>
			IXWO+IqyhuAIuFlDIkh7dujYTkQq3mkiAmliYVWDyBc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PWYvfp57Fjn115z1QgQ2u3WXXd0=
			</data>
			<key>hash2</key>
			<data>
			FEMrk3dQj6ETqtPcUYdje3/2YMP1+vkcma9AzhhwMmA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Kz6CRitjor6eDbhbEEqNpb5J7rE=
			</data>
			<key>hash2</key>
			<data>
			AvWsO6kIkfRUeULN/NbFW2B55AWvN9TOzLKRzkHyPS4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>andy-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			4jcmNT2NgfK5sDTc6WBk14nGqiU=
			</data>
			<key>hash2</key>
			<data>
			bfeTDRYdYnmpm4LVZBW3T/STJRMuT4WWLOyWDKGMj98=
			</data>
		</dict>
		<key>andy-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			1q9gV9plEJkY1/VBOwPlOEpsbzQ=
			</data>
			<key>hash2</key>
			<data>
			xnrLLoUyTV2Fe+h5Yl8d0xy0uYF6xdXzfbLDTag9qak=
			</data>
		</dict>
		<key>andy-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			Y+w8FBhrkhcbFJtP937+cb3eGyQ=
			</data>
			<key>hash2</key>
			<data>
			DL9PbBsFnpzfYjdjaOuDbWZu6m4CxPmTjAQPxgNAikw=
			</data>
		</dict>
		<key>base1-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			JkEC572cv5WTKSHKxpNuo82gNYI=
			</data>
			<key>hash2</key>
			<data>
			llP2PBXsqb/qhKZO+29WAj8/Nc2W4fg7AcvN4lpIV7Y=
			</data>
		</dict>
		<key>bricks-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			P1+SaemHjcyujmgUm1t/FwGxYWk=
			</data>
			<key>hash2</key>
			<data>
			ea48oycLwxivoNVnBYJDO77RZOg6fIsm5pCHkCB77Xo=
			</data>
		</dict>
		<key>bricks-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			nyzxG2TyHd5NtldIqNnUQM/aon8=
			</data>
			<key>hash2</key>
			<data>
			bCYpgLO+nN/NkKOIermt+okPuix5IISaL04OTb6Vh/M=
			</data>
		</dict>
		<key>bricks-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			VzPY0XwcukSTzgAF+CiU+gZsnxw=
			</data>
			<key>hash2</key>
			<data>
			4CXgh4wKH2hjkWh8E7lDzvUZKlak7cvih1rTegx+e68=
			</data>
		</dict>
		<key>cedar-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			Jd2PtxGD1MmcLk0cGgW9I9RpN4w=
			</data>
			<key>hash2</key>
			<data>
			8JuqXkNtL06AMBNi+chpJV3PlO3h0v+1OZR6tnSWpSM=
			</data>
		</dict>
		<key>cedar-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			nAOcGGMgqJqL5iNos2AutWeV3nc=
			</data>
			<key>hash2</key>
			<data>
			FNzaZMMd6KXQcXPLCXJD5d58sDA4/uFM+fdNRsckPxg=
			</data>
		</dict>
		<key>cedar-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			aHOIurzBqkzl3ksBfe8gS5fi1d8=
			</data>
			<key>hash2</key>
			<data>
			W+PxGURLULtPfo2IUTW3OItA/K/jp0dIDs6iv2BieYc=
			</data>
		</dict>
		<key>chroma-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			RUupW9F8QlvVdsAGvZm4Q+D20Ko=
			</data>
			<key>hash2</key>
			<data>
			BV/Q2srFePdBEs5Jl91qt+8BaWwGG5Q1bqX4i4uCtrc=
			</data>
		</dict>
		<key>chroma-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			dtx1Kc3YLWxPaHUsznENRU9tLcQ=
			</data>
			<key>hash2</key>
			<data>
			mOjA4jAC+EA680YnkPxwjyD1dF1ChrnNbWl9u2BR1oI=
			</data>
		</dict>
		<key>chroma-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			stGwudiXtOLqJl1VTczFzTGE+ZU=
			</data>
			<key>hash2</key>
			<data>
			3HxFhNDcswPFF23Ys4d0R5okU1FP9OaxwIiyCwETrac=
			</data>
		</dict>
		<key>de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LTDON6YEF7ST70ahhzpRlX+dllo=
			</data>
			<key>hash2</key>
			<data>
			pATwe6R/mTyfKctJaAAwaMfm3K4DaD96V6geODZVMeo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>depth-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			i6AHSjdO9nTgEpGKiYpKnCwd47E=
			</data>
			<key>hash2</key>
			<data>
			b3UnZq8mbt+dSiQzQVugRFiau7z8bn/T/nIN6pnGCcY=
			</data>
		</dict>
		<key>depth-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			PqAdyFaK6bIPty7YiB1cXxv7owc=
			</data>
			<key>hash2</key>
			<data>
			coaBrUG2i1+9Y/1MSHiQxJPtNktsaqZXcsd+3oOUo+Y=
			</data>
		</dict>
		<key>depth-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			yZ1JyHEvjpSoiYQc9jN7FD+49CA=
			</data>
			<key>hash2</key>
			<data>
			UszUnMNaUUEjX2SRUeXYYNVQkuayEphJNsZgmw30Pj0=
			</data>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p+nwJG17/r4UQWIvuyAQPZDS7uM=
			</data>
			<key>hash2</key>
			<data>
			OZ+EgXhddKr/9sGyvGIHTl/V+BCybQSqcXteZvuD4+c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/EHBdA5VdycM64mhhD4lJJsUVP4=
			</data>
			<key>hash2</key>
			<data>
			9aFIjTuVS8FbF2Z8ugiePol2qgBZ5UcGhyKDfXAoDfM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mSmC+D9j02gAPc1cwywbssQiBKQ=
			</data>
			<key>hash2</key>
			<data>
			tQWvVjd1KS9WQkf6ZZi78V4cdDFoKqdPfcwIGC2h9Do=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cVBkX8o0wGEzHC1mqOzEvPkmlLU=
			</data>
			<key>hash2</key>
			<data>
			upAO4eVipBmjyPr6EYVjhrBVMfDmsBNdtmZcHdUrERE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>graphite-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			YR7wQ7O/RhHvqBigzewKV/syQZQ=
			</data>
			<key>hash2</key>
			<data>
			fCox5ozYoYyHRaHgZMtoFRuz+bM+/nvINF8B+B8qXjo=
			</data>
		</dict>
		<key>graphite-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			np7EJs3SHyGXbeA+fX/cfEzy480=
			</data>
			<key>hash2</key>
			<data>
			rgbkUPziEcNyZRpVRXOIlOYwWbtOXtYkhzEJK7H0OeU=
			</data>
		</dict>
		<key>graphite-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			LqeVMAoHG+oCR5NyV/+9Hz4QJBM=
			</data>
			<key>hash2</key>
			<data>
			LTdtVyCi/S7n7PUBMXqihP6MQxFAUb0gYdH5z5EQk4E=
			</data>
		</dict>
		<key>it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2P+PjoufHmOEDNu336ve6rqLDAM=
			</data>
			<key>hash2</key>
			<data>
			L0NtRL12UKi/IFxM5VW5yZgXCd1cCx3UK0Dsfu954Og=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nELECNjh/DXfP4ElOEfZe7X4XIA=
			</data>
			<key>hash2</key>
			<data>
			zt8A4MrLCXRdOSdYL9JhCI65eY+HVMgXhhwuvUfCdmc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>karat-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			gAGkywTUQ2hFODLtmtC7gEwXP3c=
			</data>
			<key>hash2</key>
			<data>
			2Eba/ijovSgWZZGF7v+WsY3+qDfppJeTpcVE2lx/CqY=
			</data>
		</dict>
		<key>karat-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			+Mq70YusyQAn+9A4Y1D2F4AMigs=
			</data>
			<key>hash2</key>
			<data>
			wJX5kmWVgr5QeOsBS6LaBJE3kKqJ2lOpa/bxix5ToWw=
			</data>
		</dict>
		<key>karat-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			ys3gO/H606017EQix6ZrYbu97yA=
			</data>
			<key>hash2</key>
			<data>
			KxybT7xdC9+uRg1eGiPSiPUAXzBf8YFXeq8UyF3ZgFo=
			</data>
		</dict>
		<key>ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/wuFqBA3/wO0M0tKH1oW+Qlir9Q=
			</data>
			<key>hash2</key>
			<data>
			LTi1Q8RTDCQ8VLxoCadK5RZAr6ZrXz5a+v+DfjLQLds=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>monster-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			aEtpqqeq25BOAsESeZjUYMwK1YI=
			</data>
			<key>hash2</key>
			<data>
			QbpXpi9DTr1pFU5MvibHHo3z7GoLVtjXfUB3hlfJdho=
			</data>
		</dict>
		<key>monster-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			NItWhVRF0e5NKx61tLhQsS+JVgs=
			</data>
			<key>hash2</key>
			<data>
			dLFA1KF2NvvxH3bU6Akqp+psUyMVL7Fef1Qc3KpK1sk=
			</data>
		</dict>
		<key>monster-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			OTuCV/89NlfbPugIWset1pcmTAk=
			</data>
			<key>hash2</key>
			<data>
			z/j2djFJ78/Z44NBRiIeTajU1RNgUQtT5L8+AFiwPGA=
			</data>
		</dict>
		<key>normal-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			cf3RwXmcWshBcK2KLXyob6vO1j0=
			</data>
			<key>hash2</key>
			<data>
			pA7/nksYB+eZDDheRAgP1V5cptq9cqjvp6sGeFBoME4=
			</data>
		</dict>
		<key>normal-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			8YiWkSvW0zx9OVrCiilifY6iCCQ=
			</data>
			<key>hash2</key>
			<data>
			gI0frTxtP3iGd/e0g6mZntjcpBECMexDv/qeZw1a4+4=
			</data>
		</dict>
		<key>normal-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			E7YQDPKEN6UPLK8vqrW7ynr5N6U=
			</data>
			<key>hash2</key>
			<data>
			Mzj/NgiKLhZPBEdKfhNGlUerlAQsisuwoLchKTqO+RU=
			</data>
		</dict>
		<key>opal-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			JxA+QKOggC+LfCpsyltQYX8TGX8=
			</data>
			<key>hash2</key>
			<data>
			ozcv7rePcJwJE3xSEIzfY9WeuXcO+us0ezzZzZ4RAVM=
			</data>
		</dict>
		<key>opal-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			lRY8D0TWIc8/rI6FvomnxAdGv/w=
			</data>
			<key>hash2</key>
			<data>
			BjESPbFkNIrLSX/nSRYhCFy2HK6CW6gEti0/IdcQKeI=
			</data>
		</dict>
		<key>opal-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			W8U9oQe4l5m28/gHjL8ZjtVvpuc=
			</data>
			<key>hash2</key>
			<data>
			GSLwZA4IZQD8s2R3zUDJhr0fKp/zJNmwBzKHSGlklPQ=
			</data>
		</dict>
		<key>presstube-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			Jh/2brca/CFAkquNIrJuhQdDC9g=
			</data>
			<key>hash2</key>
			<data>
			dBF50qjDxBrBPguAOunECVipbRsoiOg3syhs5ql/lo8=
			</data>
		</dict>
		<key>presstube-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			hRGtshy6vCyOXnkqMXig+f4hvY8=
			</data>
			<key>hash2</key>
			<data>
			CHZfpy+4QzL3sxDr9S4a6e2lOqbaVjRMQDTFBApOa7w=
			</data>
		</dict>
		<key>presstube-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			P8bZldX26ircgfJIKVyDMzuYeK8=
			</data>
			<key>hash2</key>
			<data>
			yL6tFK1dIJsgm502PRAkP10FN4xGPIbmqbOeJlWhRWI=
			</data>
		</dict>
		<key>wireframe-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			oPf7NGvKfp9A/y1DmScXTxiRbzs=
			</data>
			<key>hash2</key>
			<data>
			NJzVFC6uZG5N9/qy+4UzDWt1zzvZiqw1ecPw0Fgh9vs=
			</data>
		</dict>
		<key>wireframe-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			SkET4REpucuQlvEopWhZgzy3xvY=
			</data>
			<key>hash2</key>
			<data>
			7DX6SYJJ8oNb30ouU3mQFIaVOXHpJr2EDpTKKerWq48=
			</data>
		</dict>
		<key>wireframe-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			8ji1OdI/yl3KdcG520CtnE7BUCE=
			</data>
			<key>hash2</key>
			<data>
			jZsIJ5Tbo5Mjwkqj+G2GWH5sLwTbFt90hIEDrk5l1O0=
			</data>
		</dict>
		<key>zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HGf0fK/p7N26ga/Y9qcO9p63+AA=
			</data>
			<key>hash2</key>
			<data>
			BSklcEBMndWgLqsUu08FMTk2H/usXtaOBMQS3EfSn18=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Shi/GjyOXaU+CsI6mdntDrdGAW0=
			</data>
			<key>hash2</key>
			<data>
			WYIHFBaN+pGrE3tpPWaqlGI4FPDPF0IZigbiVXbRHrM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+ciEm+qDGp+UZoYEmWF/ETcdQhA=
			</data>
			<key>hash2</key>
			<data>
			XjAQ/6tUd/9Oo4HuqlW1gDE2aVZnc+4gveFjkxo3G24=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>SC_Info/widget-mainExtension\.(sinf|supp|supf|supx)$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<integer>10000</integer>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>SC_Info/widget-mainExtension\.(sinf|supp|supf|supx)$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<integer>10000</integer>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
