{
  "animations": {
    "sunIn": {
      "actions": [
        {
          "nativeAnimation": "planeColor",
          "targetColor": "237 230 221"
        },
        {
          "nativeAnimation": "planeNoiseIntensity",
          "intensity": 0.0
        },
        {
          "nativeAnimation": "fogColor",
          "targetColor": "237 230 221 245"
        },
        {
          "nativeAnimation": "statusBarStyle",
          "statusBarStyle": "dark"
        }
      ]
    },
    "moonIn": {
      "actions": [
        {
          "nativeAnimation": "planeColor",
          "targetColor": "17 142 210"
        },
        {
          "nativeAnimation": "planeNoiseIntensity",
          "intensity": 0.5
        },
        {
          "nativeAnimation": "fogColor",
          "targetColor": "17 142 210 245"
        },
        {
          "nativeAnimation": "statusBarStyle",
          "statusBarStyle": "light"
        }
      ]
    },
  }
}
