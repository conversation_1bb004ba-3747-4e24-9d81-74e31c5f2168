import { useState, useEffect } from 'react';

export function useGeolocation() {
  const [location, setLocation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const getLocation = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!navigator.geolocation) {
          throw new Error('Geolocation is not supported by this browser');
        }

        // Get current position
        const position = await new Promise((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(
            resolve,
            reject,
            {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 300000 // 5 minutes
            }
          );
        });

        const { latitude, longitude } = position.coords;

        // Reverse geocoding to get city name
        try {
          const response = await fetch(
            `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
          );
          
          if (response.ok) {
            const data = await response.json();
            setLocation({
              latitude,
              longitude,
              city: data.city || data.locality || 'Unknown City',
              region: data.principalSubdivision || data.countryName,
              country: data.countryName
            });
          } else {
            // Fallback without city name
            setLocation({
              latitude,
              longitude,
              city: 'Current Location',
              region: '',
              country: ''
            });
          }
        } catch (geocodeError) {
          console.warn('Reverse geocoding failed:', geocodeError);
          // Use coordinates without city name
          setLocation({
            latitude,
            longitude,
            city: 'Current Location',
            region: '',
            country: ''
          });
        }

      } catch (err) {
        console.error('Geolocation error:', err);
        
        let errorMessage = 'Unable to get your location';
        
        switch (err.code) {
          case err.PERMISSION_DENIED:
            errorMessage = 'Location access denied. Please enable location services.';
            break;
          case err.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable.';
            break;
          case err.TIMEOUT:
            errorMessage = 'Location request timed out.';
            break;
          default:
            errorMessage = err.message || 'Unknown location error';
        }
        
        setError(errorMessage);
        
        // Fallback to default location (Seattle)
        setLocation({
          latitude: 47.6062,
          longitude: -122.3321,
          city: 'Seattle',
          region: 'WA',
          country: 'US'
        });
      } finally {
        setLoading(false);
      }
    };

    getLocation();
  }, []);

  const requestLocation = () => {
    setLoading(true);
    setError(null);
    
    // Re-trigger location request
    useEffect(() => {
      // This will re-run the location logic
    }, []);
  };

  return { 
    location, 
    loading, 
    error, 
    requestLocation 
  };
}
