import React from 'react';
import '../styles/ThemeSelector.css';

const ThemeSelector = ({ themes, currentTheme, onThemeSelect, onClose }) => {
  return (
    <div className="theme-selector-overlay" onClick={onClose}>
      <div className="theme-selector" onClick={(e) => e.stopPropagation()}>
        <div className="theme-selector-header">
          <h3>Choose Theme</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <div className="theme-grid">
          {Object.values(themes).map((theme) => (
            <div
              key={theme.id}
              className={`theme-option ${currentTheme === theme.id ? 'active' : ''}`}
              onClick={() => onThemeSelect(theme.id)}
            >
              <div 
                className="theme-preview"
                style={{ background: theme.colors.background }}
              >
                <div className="theme-preview-content">
                  <div className="preview-temp">72°</div>
                  <div className="preview-condition">Sunny</div>
                </div>
              </div>
              <div className="theme-name">{theme.name}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ThemeSelector;
