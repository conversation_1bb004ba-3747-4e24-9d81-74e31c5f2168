import React from 'react';

const WeatherUI = ({ weatherData, theme, onThemeToggle, locationError, weatherError }) => {
  if (!weatherData) {
    return (
      <div className="weather-ui">
        <div className="error-message">
          <div className="error-title">
            {locationError ? 'Location Error' : 'Weather Data Unavailable'}
          </div>
          <div className="error-description">
            {locationError || weatherError || 'Unable to load weather information'}
          </div>
        </div>
      </div>
    );
  }

  const { current, hourly, daily } = weatherData;
  const currentTime = new Date().toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: true 
  });

  return (
    <div className="weather-ui">
      {/* Header */}
      <div className="weather-header">
        <div className="location-info">
          <div className="location-name">{current.location}</div>
          <div className="current-time">{currentTime}</div>
        </div>
        <button className="theme-toggle" onClick={onThemeToggle}>
          🎨
        </button>
      </div>

      {/* Main Weather Display */}
      <div className="main-weather fade-in">
        <div className="temperature">
          {current.temperature}°
        </div>
        <div className="weather-condition">
          {current.description}
        </div>
        <div className="feels-like">
          Feels like {current.feelsLike}°
        </div>
      </div>

      {/* Bottom Panel */}
      <div className="bottom-panel slide-up">
        {/* Weather Details Grid */}
        <div className="weather-details">
          <div className="detail-item">
            <div className="detail-label">Humidity</div>
            <div className="detail-value">{current.humidity}%</div>
          </div>
          <div className="detail-item">
            <div className="detail-label">Wind</div>
            <div className="detail-value">{current.windSpeed} mph</div>
          </div>
          <div className="detail-item">
            <div className="detail-label">Pressure</div>
            <div className="detail-value">{current.pressure} in</div>
          </div>
          <div className="detail-item">
            <div className="detail-label">UV Index</div>
            <div className="detail-value">{current.uvIndex}</div>
          </div>
          <div className="detail-item">
            <div className="detail-label">Visibility</div>
            <div className="detail-value">{Math.round(current.visibility / 1000)} km</div>
          </div>
          <div className="detail-item">
            <div className="detail-label">Dew Point</div>
            <div className="detail-value">{Math.round(current.dewPoint)}°</div>
          </div>
        </div>

        {/* Hourly Forecast */}
        <div className="hourly-forecast">
          <div className="hourly-scroll">
            {hourly.map((hour, index) => (
              <div key={index} className="hourly-item">
                <div className="hourly-time">{hour.time}</div>
                <div className="hourly-icon">
                  {getWeatherIcon(hour.condition)}
                </div>
                <div className="hourly-temp">{hour.temp}°</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper function to get weather icons
const getWeatherIcon = (condition) => {
  const icons = {
    'clear': '☀️',
    'sunny': '☀️',
    'partly-cloudy': '⛅',
    'cloudy': '☁️',
    'overcast': '☁️',
    'rain': '🌧️',
    'drizzle': '🌦️',
    'thunderstorm': '⛈️',
    'snow': '❄️',
    'fog': '🌫️',
    'mist': '🌫️',
    'haze': '🌫️'
  };
  
  return icons[condition] || '☀️';
};

export default WeatherUI;
