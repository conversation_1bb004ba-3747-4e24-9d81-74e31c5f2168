import React, { useRef, useMemo, useEffect } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import { Points, PointMaterial, Sphere, MeshDistortMaterial } from '@react-three/drei';
import * as THREE from 'three';
import { gsap } from 'gsap';
import VisualEffects from './VisualEffects';

const WeatherScene = ({ weatherData, theme }) => {
  const particlesRef = useRef();
  const cloudsRef = useRef();
  const backgroundRef = useRef();
  const { scene, camera } = useThree();

  if (!weatherData) return null;

  const condition = weatherData.current.condition;

  // Enhanced particle count based on device performance
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  const particleCount = isMobile ? Math.min(theme.particles.count, 150) : theme.particles.count;
  
  // Generate particles based on weather condition
  const particles = useMemo(() => {
    const count = particleCount;
    const positions = new Float32Array(count * 3);
    const velocities = new Float32Array(count * 3);
    const sizes = new Float32Array(count);
    const colors = new Float32Array(count * 3);

    for (let i = 0; i < count; i++) {
      const i3 = i * 3;

      // Random positions in a larger sphere
      const radius = 15 + Math.random() * 10;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      positions[i3] = radius * Math.sin(phi) * Math.cos(theta);
      positions[i3 + 1] = radius * Math.cos(phi);
      positions[i3 + 2] = radius * Math.sin(phi) * Math.sin(theta);

      // Particle sizes
      sizes[i] = Math.random() * 0.5 + 0.2;
      
      // Velocities based on weather condition
      switch (condition) {
        case 'rain':
        case 'drizzle':
          velocities[i3] = (Math.random() - 0.5) * 0.02;
          velocities[i3 + 1] = -Math.random() * 0.1 - 0.05;
          velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;
          break;
        case 'snow':
          velocities[i3] = (Math.random() - 0.5) * 0.01;
          velocities[i3 + 1] = -Math.random() * 0.03 - 0.01;
          velocities[i3 + 2] = (Math.random() - 0.5) * 0.01;
          break;
        case 'cloudy':
        case 'partly-cloudy':
          velocities[i3] = (Math.random() - 0.5) * 0.005;
          velocities[i3 + 1] = (Math.random() - 0.5) * 0.005;
          velocities[i3 + 2] = (Math.random() - 0.5) * 0.005;
          break;
        default:
          velocities[i3] = (Math.random() - 0.5) * 0.002;
          velocities[i3 + 1] = (Math.random() - 0.5) * 0.002;
          velocities[i3 + 2] = (Math.random() - 0.5) * 0.002;
      }
    }
    
    return { positions, velocities };
  }, [condition, theme.particles.count]);

  // Animate particles
  useFrame((state, delta) => {
    if (particlesRef.current) {
      const positions = particlesRef.current.geometry.attributes.position.array;
      
      for (let i = 0; i < positions.length; i += 3) {
        // Update positions based on velocities
        positions[i] += particles.velocities[i] * delta * 60;
        positions[i + 1] += particles.velocities[i + 1] * delta * 60;
        positions[i + 2] += particles.velocities[i + 2] * delta * 60;
        
        // Reset particles that go out of bounds
        if (positions[i + 1] < -10) {
          positions[i] = (Math.random() - 0.5) * 20;
          positions[i + 1] = 10;
          positions[i + 2] = (Math.random() - 0.5) * 20;
        }
        
        if (Math.abs(positions[i]) > 10 || Math.abs(positions[i + 2]) > 10) {
          positions[i] = (Math.random() - 0.5) * 20;
          positions[i + 1] = (Math.random() - 0.5) * 20;
          positions[i + 2] = (Math.random() - 0.5) * 20;
        }
      }
      
      particlesRef.current.geometry.attributes.position.needsUpdate = true;
    }
    
    // Rotate clouds slowly
    if (cloudsRef.current) {
      cloudsRef.current.rotation.y += 0.001;
    }
  });

  // Get particle color based on weather condition
  const getParticleColor = () => {
    switch (condition) {
      case 'rain':
      case 'drizzle':
        return theme.colors.rain;
      case 'snow':
        return theme.colors.snow;
      case 'cloudy':
      case 'partly-cloudy':
        return theme.colors.clouds;
      default:
        return theme.colors.primary;
    }
  };

  // Enhanced lighting setup
  const getLightingForCondition = () => {
    switch (condition) {
      case 'clear':
      case 'sunny':
        return { ambient: 0.8, directional: 1.2, color: '#FFD700' };
      case 'cloudy':
      case 'overcast':
        return { ambient: 0.4, directional: 0.6, color: '#87CEEB' };
      case 'rain':
      case 'drizzle':
        return { ambient: 0.3, directional: 0.5, color: '#4682B4' };
      case 'snow':
        return { ambient: 0.6, directional: 0.8, color: '#F0F8FF' };
      default:
        return { ambient: 0.6, directional: 0.8, color: '#FFFFFF' };
    }
  };

  const lighting = getLightingForCondition();

  return (
    <>
      {/* Enhanced lighting */}
      <ambientLight intensity={lighting.ambient} color={lighting.color} />
      <directionalLight
        position={[10, 10, 5]}
        intensity={lighting.directional}
        color={lighting.color}
        castShadow
      />
      <pointLight position={[-10, -10, -5]} intensity={0.3} color="#4A90E2" />

      {/* Dynamic background sphere */}
      <Sphere ref={backgroundRef} args={[50, 32, 32]}>
        <MeshDistortMaterial
          color={new THREE.Color().setStyle(theme.colors.background.split(',')[0].split('(')[1] || '#667eea')}
          side={THREE.BackSide}
          distort={0.1}
          speed={0.5}
          roughness={0.8}
        />
      </Sphere>
      
      {/* Enhanced Visual Effects */}
      <VisualEffects condition={condition} theme={theme} />

      {/* Ambient particles */}
      <Points ref={particlesRef}>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={particles.positions.length / 3}
            array={particles.positions}
            itemSize={3}
          />
        </bufferGeometry>
        <PointMaterial
          size={theme.particles.size * 0.5}
          color={getParticleColor()}
          transparent
          opacity={0.4}
          sizeAttenuation
        />
      </Points>
      
      {/* Clouds for cloudy weather */}
      {(condition === 'cloudy' || condition === 'partly-cloudy') && (
        <group ref={cloudsRef}>
          {[...Array(5)].map((_, i) => (
            <Cloud
              key={i}
              position={[
                (Math.random() - 0.5) * 15,
                (Math.random() - 0.5) * 8,
                (Math.random() - 0.5) * 15
              ]}
              scale={Math.random() * 0.5 + 0.5}
              color={theme.colors.clouds}
            />
          ))}
        </group>
      )}
      
      {/* Sun for clear weather */}
      {(condition === 'clear' || condition === 'sunny') && (
        <Sun position={[5, 5, -5]} color={theme.colors.sun} />
      )}
    </>
  );
};

// Cloud component
const Cloud = ({ position, scale, color }) => {
  const cloudRef = useRef();
  
  useFrame((state) => {
    if (cloudRef.current) {
      cloudRef.current.position.x += Math.sin(state.clock.elapsedTime * 0.1) * 0.001;
      cloudRef.current.rotation.y += 0.002;
    }
  });
  
  return (
    <group ref={cloudRef} position={position} scale={scale}>
      {[...Array(8)].map((_, i) => (
        <mesh
          key={i}
          position={[
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 1,
            (Math.random() - 0.5) * 2
          ]}
        >
          <sphereGeometry args={[Math.random() * 0.5 + 0.3, 8, 8]} />
          <meshLambertMaterial 
            color={color} 
            transparent 
            opacity={0.3}
          />
        </mesh>
      ))}
    </group>
  );
};

// Sun component
const Sun = ({ position, color }) => {
  const sunRef = useRef();
  
  useFrame((state) => {
    if (sunRef.current) {
      sunRef.current.rotation.y += 0.01;
      const scale = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.1;
      sunRef.current.scale.setScalar(scale);
    }
  });
  
  return (
    <mesh ref={sunRef} position={position}>
      <sphereGeometry args={[1, 16, 16]} />
      <meshBasicMaterial 
        color={color} 
        transparent 
        opacity={0.8}
      />
    </mesh>
  );
};

export default WeatherScene;
