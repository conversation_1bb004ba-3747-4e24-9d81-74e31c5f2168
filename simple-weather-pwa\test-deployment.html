<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Weather PWA - Deployment Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
        }
        h1 { text-align: center; margin-bottom: 30px; }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }
        .test-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 8px;
        }
        .status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            display: inline-block;
        }
        .pass { background: #4CAF50; }
        .fail { background: #f44336; }
        .pending { background: #ff9800; }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { opacity: 0.9; }
        .url-input {
            width: 100%;
            padding: 10px;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.1);
            color: white;
            margin: 10px 0;
        }
        .url-input::placeholder { color: rgba(255,255,255,0.7); }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌤️ Simple Weather PWA - Deployment Test</h1>
        
        <div class="test-section">
            <h3>🔗 Deployment URL</h3>
            <input type="text" class="url-input" id="deployUrl" 
                   placeholder="https://YOUR_USERNAME.github.io/simple-weather-pwa/"
                   value="">
            <button onclick="setUrl()">Set URL</button>
            <button onclick="openApp()">Open App</button>
        </div>

        <div class="test-section">
            <h3>🧪 Automated Tests</h3>
            <button onclick="runAllTests()">Run All Tests</button>
            <button onclick="clearResults()">Clear Results</button>
            
            <div id="testResults">
                <div class="test-item">
                    <span class="status pending" id="status-load"></span>
                    <span>Site loads successfully</span>
                </div>
                <div class="test-item">
                    <span class="status pending" id="status-manifest"></span>
                    <span>PWA manifest accessible</span>
                </div>
                <div class="test-item">
                    <span class="status pending" id="status-sw"></span>
                    <span>Service worker registers</span>
                </div>
                <div class="test-item">
                    <span class="status pending" id="status-https"></span>
                    <span>Served over HTTPS</span>
                </div>
                <div class="test-item">
                    <span class="status pending" id="status-responsive"></span>
                    <span>Responsive design</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📱 Manual PWA Tests</h3>
            <div class="test-item">
                <span class="status pending"></span>
                <span>Install prompt appears</span>
            </div>
            <div class="test-item">
                <span class="status pending"></span>
                <span>App installs successfully</span>
            </div>
            <div class="test-item">
                <span class="status pending"></span>
                <span>Works offline</span>
            </div>
            <div class="test-item">
                <span class="status pending"></span>
                <span>3D weather effects work</span>
            </div>
            <div class="test-item">
                <span class="status pending"></span>
                <span>Theme switching works</span>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 Performance Check</h3>
            <button onclick="checkPerformance()">Check Performance</button>
            <button onclick="openLighthouse()">Run Lighthouse</button>
        </div>

        <div class="results" id="results" style="display: none;">
            <h4>Test Results:</h4>
            <pre id="resultText"></pre>
        </div>
    </div>

    <script>
        let deploymentUrl = '';

        function setUrl() {
            deploymentUrl = document.getElementById('deployUrl').value;
            if (!deploymentUrl.startsWith('http')) {
                deploymentUrl = 'https://' + deploymentUrl;
            }
            document.getElementById('deployUrl').value = deploymentUrl;
            log('URL set to: ' + deploymentUrl);
        }

        function openApp() {
            if (!deploymentUrl) {
                alert('Please set the deployment URL first');
                return;
            }
            window.open(deploymentUrl, '_blank');
        }

        function log(message) {
            const results = document.getElementById('results');
            const resultText = document.getElementById('resultText');
            results.style.display = 'block';
            resultText.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }

        function setStatus(testId, status) {
            const element = document.getElementById('status-' + testId);
            if (element) {
                element.className = 'status ' + status;
            }
        }

        async function runAllTests() {
            if (!deploymentUrl) {
                alert('Please set the deployment URL first');
                return;
            }

            log('Starting automated tests...');
            
            // Test 1: Site loads
            try {
                const response = await fetch(deploymentUrl, { mode: 'no-cors' });
                setStatus('load', 'pass');
                log('✅ Site loads successfully');
            } catch (error) {
                setStatus('load', 'fail');
                log('❌ Site failed to load: ' + error.message);
            }

            // Test 2: HTTPS
            if (deploymentUrl.startsWith('https://')) {
                setStatus('https', 'pass');
                log('✅ Served over HTTPS');
            } else {
                setStatus('https', 'fail');
                log('❌ Not served over HTTPS');
            }

            // Test 3: Manifest
            try {
                const manifestUrl = deploymentUrl + (deploymentUrl.endsWith('/') ? '' : '/') + 'manifest.webmanifest';
                const response = await fetch(manifestUrl, { mode: 'no-cors' });
                setStatus('manifest', 'pass');
                log('✅ PWA manifest accessible');
            } catch (error) {
                setStatus('manifest', 'fail');
                log('❌ PWA manifest not accessible');
            }

            // Test 4: Service Worker
            try {
                const swUrl = deploymentUrl + (deploymentUrl.endsWith('/') ? '' : '/') + 'sw.js';
                const response = await fetch(swUrl, { mode: 'no-cors' });
                setStatus('sw', 'pass');
                log('✅ Service worker file accessible');
            } catch (error) {
                setStatus('sw', 'fail');
                log('❌ Service worker not accessible');
            }

            // Test 5: Responsive (basic check)
            setStatus('responsive', 'pass');
            log('✅ Responsive design (manual verification needed)');

            log('Automated tests completed!');
        }

        function clearResults() {
            document.getElementById('resultText').textContent = '';
            document.getElementById('results').style.display = 'none';
            
            // Reset all status indicators
            const statuses = document.querySelectorAll('.status');
            statuses.forEach(status => {
                if (status.id.startsWith('status-')) {
                    status.className = 'status pending';
                }
            });
        }

        function checkPerformance() {
            if (!deploymentUrl) {
                alert('Please set the deployment URL first');
                return;
            }
            
            log('Performance check initiated...');
            log('Manual verification needed:');
            log('- Check Network tab for load times');
            log('- Monitor FPS in Performance tab');
            log('- Verify bundle size in Network tab');
        }

        function openLighthouse() {
            if (!deploymentUrl) {
                alert('Please set the deployment URL first');
                return;
            }
            
            const lighthouseUrl = `https://pagespeed.web.dev/report?url=${encodeURIComponent(deploymentUrl)}`;
            window.open(lighthouseUrl, '_blank');
            log('Lighthouse test opened in new tab');
        }

        // Auto-detect GitHub Pages URL pattern
        window.onload = function() {
            const currentUrl = window.location.href;
            if (currentUrl.includes('github.io')) {
                const baseUrl = currentUrl.replace('/test-deployment.html', '');
                document.getElementById('deployUrl').value = baseUrl;
                log('Auto-detected GitHub Pages URL: ' + baseUrl);
            }
        };
    </script>
</body>
</html>
