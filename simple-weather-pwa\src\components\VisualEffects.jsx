import React, { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import { Points, PointMaterial, Sphere } from '@react-three/drei';
import * as THREE from 'three';

const VisualEffects = ({ condition, theme }) => {
  const effectsRef = useRef();
  
  // Create enhanced particle effects based on weather
  const createWeatherEffect = () => {
    switch (condition) {
      case 'rain':
      case 'drizzle':
        return <RainEffect theme={theme} />;
      case 'snow':
        return <SnowEffect theme={theme} />;
      case 'thunderstorm':
        return <ThunderstormEffect theme={theme} />;
      case 'clear':
      case 'sunny':
        return <SunEffect theme={theme} />;
      case 'cloudy':
      case 'partly-cloudy':
        return <CloudEffect theme={theme} />;
      default:
        return null;
    }
  };

  return (
    <group ref={effectsRef}>
      {createWeatherEffect()}
    </group>
  );
};

// Rain effect with realistic droplets
const RainEffect = ({ theme }) => {
  const rainRef = useRef();
  
  const rainParticles = useMemo(() => {
    const count = 200;
    const positions = new Float32Array(count * 3);
    const velocities = new Float32Array(count * 3);
    
    for (let i = 0; i < count; i++) {
      const i3 = i * 3;
      positions[i3] = (Math.random() - 0.5) * 30;
      positions[i3 + 1] = Math.random() * 20 + 10;
      positions[i3 + 2] = (Math.random() - 0.5) * 30;
      
      velocities[i3] = (Math.random() - 0.5) * 0.1;
      velocities[i3 + 1] = -Math.random() * 0.3 - 0.2;
      velocities[i3 + 2] = (Math.random() - 0.5) * 0.1;
    }
    
    return { positions, velocities };
  }, []);

  useFrame((state, delta) => {
    if (rainRef.current) {
      const positions = rainRef.current.geometry.attributes.position.array;
      
      for (let i = 0; i < positions.length; i += 3) {
        positions[i] += rainParticles.velocities[i] * delta * 60;
        positions[i + 1] += rainParticles.velocities[i + 1] * delta * 60;
        positions[i + 2] += rainParticles.velocities[i + 2] * delta * 60;
        
        if (positions[i + 1] < -10) {
          positions[i] = (Math.random() - 0.5) * 30;
          positions[i + 1] = 20;
          positions[i + 2] = (Math.random() - 0.5) * 30;
        }
      }
      
      rainRef.current.geometry.attributes.position.needsUpdate = true;
    }
  });

  return (
    <Points ref={rainRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={rainParticles.positions.length / 3}
          array={rainParticles.positions}
          itemSize={3}
        />
      </bufferGeometry>
      <PointMaterial
        size={0.05}
        color={theme.colors.rain}
        transparent
        opacity={0.8}
        sizeAttenuation
      />
    </Points>
  );
};

// Snow effect with gentle floating
const SnowEffect = ({ theme }) => {
  const snowRef = useRef();
  
  const snowParticles = useMemo(() => {
    const count = 150;
    const positions = new Float32Array(count * 3);
    const velocities = new Float32Array(count * 3);
    
    for (let i = 0; i < count; i++) {
      const i3 = i * 3;
      positions[i3] = (Math.random() - 0.5) * 25;
      positions[i3 + 1] = Math.random() * 20 + 5;
      positions[i3 + 2] = (Math.random() - 0.5) * 25;
      
      velocities[i3] = (Math.random() - 0.5) * 0.02;
      velocities[i3 + 1] = -Math.random() * 0.05 - 0.01;
      velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;
    }
    
    return { positions, velocities };
  }, []);

  useFrame((state, delta) => {
    if (snowRef.current) {
      const positions = snowRef.current.geometry.attributes.position.array;
      
      for (let i = 0; i < positions.length; i += 3) {
        positions[i] += Math.sin(state.clock.elapsedTime + i) * 0.001;
        positions[i + 1] += snowParticles.velocities[i + 1] * delta * 60;
        positions[i + 2] += Math.cos(state.clock.elapsedTime + i) * 0.001;
        
        if (positions[i + 1] < -5) {
          positions[i] = (Math.random() - 0.5) * 25;
          positions[i + 1] = 15;
          positions[i + 2] = (Math.random() - 0.5) * 25;
        }
      }
      
      snowRef.current.geometry.attributes.position.needsUpdate = true;
    }
  });

  return (
    <Points ref={snowRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={snowParticles.positions.length / 3}
          array={snowParticles.positions}
          itemSize={3}
        />
      </bufferGeometry>
      <PointMaterial
        size={0.08}
        color={theme.colors.snow}
        transparent
        opacity={0.9}
        sizeAttenuation
      />
    </Points>
  );
};

// Enhanced sun with glow effect
const SunEffect = ({ theme }) => {
  const sunRef = useRef();
  const glowRef = useRef();
  
  useFrame((state) => {
    if (sunRef.current) {
      sunRef.current.rotation.y += 0.005;
      const scale = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.05;
      sunRef.current.scale.setScalar(scale);
    }
    
    if (glowRef.current) {
      const glowScale = 1.5 + Math.sin(state.clock.elapsedTime * 1.5) * 0.1;
      glowRef.current.scale.setScalar(glowScale);
      glowRef.current.material.opacity = 0.3 + Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });
  
  return (
    <group position={[8, 6, -8]}>
      {/* Main sun */}
      <Sphere ref={sunRef} args={[1.2, 32, 32]}>
        <meshBasicMaterial 
          color={theme.colors.sun} 
          transparent 
          opacity={0.9}
        />
      </Sphere>
      
      {/* Sun glow */}
      <Sphere ref={glowRef} args={[2, 16, 16]}>
        <meshBasicMaterial 
          color={theme.colors.sun} 
          transparent 
          opacity={0.2}
        />
      </Sphere>
    </group>
  );
};

// Enhanced cloud effect
const CloudEffect = ({ theme }) => {
  const cloudsRef = useRef();
  
  useFrame((state) => {
    if (cloudsRef.current) {
      cloudsRef.current.rotation.y += 0.0005;
      cloudsRef.current.position.x = Math.sin(state.clock.elapsedTime * 0.1) * 0.5;
    }
  });
  
  return (
    <group ref={cloudsRef}>
      {[...Array(8)].map((_, i) => (
        <CloudPuff
          key={i}
          position={[
            (Math.random() - 0.5) * 20,
            Math.random() * 8 + 2,
            (Math.random() - 0.5) * 20
          ]}
          scale={Math.random() * 0.8 + 0.4}
          color={theme.colors.clouds}
        />
      ))}
    </group>
  );
};

// Individual cloud puff
const CloudPuff = ({ position, scale, color }) => {
  const puffRef = useRef();
  
  useFrame((state) => {
    if (puffRef.current) {
      puffRef.current.position.x += Math.sin(state.clock.elapsedTime * 0.2) * 0.001;
      puffRef.current.rotation.y += 0.001;
    }
  });
  
  return (
    <group ref={puffRef} position={position} scale={scale}>
      {[...Array(12)].map((_, i) => (
        <Sphere
          key={i}
          args={[Math.random() * 0.8 + 0.4, 8, 8]}
          position={[
            (Math.random() - 0.5) * 3,
            (Math.random() - 0.5) * 1.5,
            (Math.random() - 0.5) * 3
          ]}
        >
          <meshLambertMaterial 
            color={color} 
            transparent 
            opacity={0.4}
          />
        </Sphere>
      ))}
    </group>
  );
};

// Thunderstorm effect (placeholder for future enhancement)
const ThunderstormEffect = ({ theme }) => {
  return (
    <group>
      <RainEffect theme={theme} />
      <CloudEffect theme={theme} />
    </group>
  );
};

export default VisualEffects;
