<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AppConfig.json</key>
		<data>
		2fxwgWzWQ11pw411ORNTjc1pwes=
		</data>
		<key><EMAIL></key>
		<data>
		mD0mejE6oT3G5NajL8lph4kaom4=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		5Qkv3D/08KpgAfJUHNhPDVh3tkk=
		</data>
		<key>Assets.car</key>
		<data>
		2aB4nhIVSsS+UwpiGTNWxq8Btmc=
		</data>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<data>
		xsX3gt/kMlLTDyjiJhLRD4DdLuQ=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		MDrKFvFWroTb0+KEbQShBcoBvo4=
		</data>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<data>
		6YQ54LP2uWyXP6slcH/HQRTrr2M=
		</data>
		<key>DemoData.json</key>
		<data>
		O+FsaJ0BZDWsYv3oV0gwpLSIx1M=
		</data>
		<key>FirstLaunchIntro.mp4</key>
		<data>
		w+pOOQcsW6co58XKrPcUZ1lqNMw=
		</data>
		<key>FoundersGrotesk-Bold.otf</key>
		<data>
		2onx4TVf2oQUN/N+kWXCX1bMBVM=
		</data>
		<key>FoundersGrotesk-BoldItalic.otf</key>
		<data>
		CX5j2Du1XPDvIS+rxvRbLIFobr0=
		</data>
		<key>FoundersGrotesk-Regular.otf</key>
		<data>
		3BnSp4IubDZkP8RBbw1SrpzBg6o=
		</data>
		<key>FoundersGrotesk-RegularItalic.otf</key>
		<data>
		QDUI6uUJj6osKw42qy7fb2XZE3g=
		</data>
		<key>Info.plist</key>
		<data>
		zayZ2yO7bQ35s6vyVddBwAAGVzk=
		</data>
		<key>JetBrainsMono-ExtraBold.ttf</key>
		<data>
		ObWoBva1HqhjvOIzGI2sKR/CGtQ=
		</data>
		<key>JetBrainsMono-Regular.ttf</key>
		<data>
		1j47G+nzKSg93KFFVPTMf5qjKJw=
		</data>
		<key>Launch Screen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		JPbtj0W9QAaAmbtV1Xt2XLxrgOY=
		</data>
		<key>Launch Screen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Launch Screen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>NeueMaticCompressed-Bold.otf</key>
		<data>
		OpjwhMvzSHZwHzQZ4KXPGPPR8Z0=
		</data>
		<key>NeumaticGothic-Bold.otf</key>
		<data>
		clj2KxA28If2JnI5WvBwna+al4w=
		</data>
		<key>NeumaticGothic-Regular.otf</key>
		<data>
		TxOmeQAZGG1iqBunXY2xcE9Pr/4=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>PlugIns/widget-mainExtension.appex/Assets.car</key>
		<data>
		2KnS9FqtJMxN/zDdXi5HWwC4s9k=
		</data>
		<key>PlugIns/widget-mainExtension.appex/FoundersGrotesk-Bold.otf</key>
		<data>
		2onx4TVf2oQUN/N+kWXCX1bMBVM=
		</data>
		<key>PlugIns/widget-mainExtension.appex/FoundersGrotesk-BoldItalic.otf</key>
		<data>
		CX5j2Du1XPDvIS+rxvRbLIFobr0=
		</data>
		<key>PlugIns/widget-mainExtension.appex/FoundersGrotesk-Regular.otf</key>
		<data>
		3BnSp4IubDZkP8RBbw1SrpzBg6o=
		</data>
		<key>PlugIns/widget-mainExtension.appex/FoundersGrotesk-RegularItalic.otf</key>
		<data>
		QDUI6uUJj6osKw42qy7fb2XZE3g=
		</data>
		<key>PlugIns/widget-mainExtension.appex/Info.plist</key>
		<data>
		nDvSjMXwivEwMX/EUMsSruR2iHc=
		</data>
		<key>PlugIns/widget-mainExtension.appex/JetBrainsMono-Regular.ttf</key>
		<data>
		1j47G+nzKSg93KFFVPTMf5qjKJw=
		</data>
		<key>PlugIns/widget-mainExtension.appex/NeueMaticCompressed-Bold.ttf</key>
		<data>
		2k9mO0xPIw+HDws6/dnu1Bhzfug=
		</data>
		<key>PlugIns/widget-mainExtension.appex/NeumaticGothic-Bold.ttf</key>
		<data>
		EYsj/rWvyLxnVzQKDxB09HVSmKE=
		</data>
		<key>PlugIns/widget-mainExtension.appex/NeumaticGothic-Regular.ttf</key>
		<data>
		Zqsr2ZsfnhaBOC7iTrsFx5xImvI=
		</data>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCat.bundle/Info.plist</key>
		<data>
		MVZ+U0wtJQF7b0Wc1vq1FzkY86I=
		</data>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCat.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		uqRDkW3s33psRj8H+jal4YSzsvk=
		</data>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/Assets.car</key>
		<data>
		VkivHEN0Sq7H7WpXWg2/23RU0nY=
		</data>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/Info.plist</key>
		<data>
		1t40HiElje0wTOPeNb3VuynXqd8=
		</data>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IgrDYE7s5Ao1kgXGWhkmE6DtaSw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/background.jpg</key>
		<data>
		cobmQyDEiwBkOm53urv6Z0Jheeg=
		</data>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/bg.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1DK4kp//KoPu6EliBCoHJSm3hSg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+0WdXT9iA7rxmekkeFV1CFfUr0M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzfk/vzfrNFlQAV78mNv+sM+cLs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/da.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Tb1M+JlXwha6CASNZN99jg1lCjY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+1wNXbdjC5n5rXHD3ujvnjU2aOw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/el.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			j7zTilv4aawn1JvdeWDti6lfvpY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/en_AU.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/en_CA.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/en_GB.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/en_US.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/es_419.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rp8pvRolRrgVJOqfZRqqPqT/zB0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/es_ES.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rp8pvRolRrgVJOqfZRqqPqT/zB0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zPSCYzm5sUbOkWRJLBpML1c7bpI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/fr_CA.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fwtMweVYC4tNUSn94QdCECWfosc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/he.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OOwIfBI8gTKKCd6IG0lAxSVfGHI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/hi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XoLnoVnrb8rYzqZkbWn4Y4jmyoM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/hr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v69QiECFoEXKsPyKgwTM6/0+988=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7U3zm8yKWi31CkYV5toBCx2pHfA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/id.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dTay5oBh44GUzpUfM/MYxpycpZk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oq1WnovzEqv8jMG/r3GzZdYL9bg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mhl5cJsbiu8E630s+IBtqg/rGS8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/kk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p+pJpe7nUaOnu2TSmYQ/7On9YrI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xMJ1BrfbetANxc5M2m4JLDeGvBY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ms.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GJ013SGsBV29jWSP6lMoiNKrlsE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rj/1cU0FueMctW5Idlc4DffFL4A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/no.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ohd0nuuHYRbQH5m7Iav1VkCBIhE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Y7VJO0AodP4+HUtD99S53Fo1eE0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/pt_BR.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ouomTBJZPP2rz7ePbMzBtD4zVCk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/pt_PT.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ouomTBJZPP2rz7ePbMzBtD4zVCk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XdJv/J/wOKxJMc4m1bBz6QiG8xA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gwj1mlzWt5aADE1iE+9KDdkAzhw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TfCNaaj825wCwvrXLJ8bozt6Mxo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5stHn6nvQv0kKmzer+shwoxHgVw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/th.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8fWP6ZGNpnRL2mozleLAHZu+asE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AyHk7CPwbQ/0lK4wnv+2YTBsYmE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/uk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uXBx8YT+kSTOMpZhEST69s90wIc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/vi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9b8184pr3yNjMqWMKU8oyGTuGjE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/zh_Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XfpxFjPoJ1mnulmHM595TUxkDfs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/zh_Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0DPdeshPj2dIZd5BIzKTZhyQmgg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/Assets.car</key>
		<data>
		QuY1SRVaDhGUKRnTDG7pGEGtTGk=
		</data>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/Info.plist</key>
		<data>
		icomyb6ys7NNbVOtazV8NyG9qCo=
		</data>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zzQ2NXhAgc2EwqNSrghGyx75Zsk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CbHT+zxH4kLofQyjzZqOn9U/XuU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PEPEDUKRjYVTc9rZmKejmxuAu3M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/explode-open.ahap</key>
		<data>
		VAAR35KfKPUX5E+tFwedxZlNJUo=
		</data>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qwFbqEe7kyM+4s62SMsPOhNdEUo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qSuDCVKNGwFjGLeFtsK9eMCV4a8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7HVCabV9EkQSS02uDL+rXCxyXOY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i91b5xMeECm4CpmeJQpIHYXuM9Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PWYvfp57Fjn115z1QgQ2u3WXXd0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Kz6CRitjor6eDbhbEEqNpb5J7rE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/_CodeSignature/CodeResources</key>
		<data>
		7sfD+eWWzIC/V9yyT69JL67U4uE=
		</data>
		<key>PlugIns/widget-mainExtension.appex/andy-animation.json</key>
		<data>
		4jcmNT2NgfK5sDTc6WBk14nGqiU=
		</data>
		<key>PlugIns/widget-mainExtension.appex/andy-colors.json</key>
		<data>
		1q9gV9plEJkY1/VBOwPlOEpsbzQ=
		</data>
		<key>PlugIns/widget-mainExtension.appex/andy-theme.json</key>
		<data>
		Y+w8FBhrkhcbFJtP937+cb3eGyQ=
		</data>
		<key>PlugIns/widget-mainExtension.appex/base1-animation.json</key>
		<data>
		JkEC572cv5WTKSHKxpNuo82gNYI=
		</data>
		<key>PlugIns/widget-mainExtension.appex/bricks-animation.json</key>
		<data>
		P1+SaemHjcyujmgUm1t/FwGxYWk=
		</data>
		<key>PlugIns/widget-mainExtension.appex/bricks-colors.json</key>
		<data>
		nyzxG2TyHd5NtldIqNnUQM/aon8=
		</data>
		<key>PlugIns/widget-mainExtension.appex/bricks-theme.json</key>
		<data>
		VzPY0XwcukSTzgAF+CiU+gZsnxw=
		</data>
		<key>PlugIns/widget-mainExtension.appex/cedar-animation.json</key>
		<data>
		Jd2PtxGD1MmcLk0cGgW9I9RpN4w=
		</data>
		<key>PlugIns/widget-mainExtension.appex/cedar-colors.json</key>
		<data>
		nAOcGGMgqJqL5iNos2AutWeV3nc=
		</data>
		<key>PlugIns/widget-mainExtension.appex/cedar-theme.json</key>
		<data>
		aHOIurzBqkzl3ksBfe8gS5fi1d8=
		</data>
		<key>PlugIns/widget-mainExtension.appex/chroma-animation.json</key>
		<data>
		RUupW9F8QlvVdsAGvZm4Q+D20Ko=
		</data>
		<key>PlugIns/widget-mainExtension.appex/chroma-colors.json</key>
		<data>
		dtx1Kc3YLWxPaHUsznENRU9tLcQ=
		</data>
		<key>PlugIns/widget-mainExtension.appex/chroma-theme.json</key>
		<data>
		stGwudiXtOLqJl1VTczFzTGE+ZU=
		</data>
		<key>PlugIns/widget-mainExtension.appex/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LTDON6YEF7ST70ahhzpRlX+dllo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/depth-animation.json</key>
		<data>
		i6AHSjdO9nTgEpGKiYpKnCwd47E=
		</data>
		<key>PlugIns/widget-mainExtension.appex/depth-colors.json</key>
		<data>
		PqAdyFaK6bIPty7YiB1cXxv7owc=
		</data>
		<key>PlugIns/widget-mainExtension.appex/depth-theme.json</key>
		<data>
		yZ1JyHEvjpSoiYQc9jN7FD+49CA=
		</data>
		<key>PlugIns/widget-mainExtension.appex/en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p+nwJG17/r4UQWIvuyAQPZDS7uM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/EHBdA5VdycM64mhhD4lJJsUVP4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mSmC+D9j02gAPc1cwywbssQiBKQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cVBkX8o0wGEzHC1mqOzEvPkmlLU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/graphite-animation.json</key>
		<data>
		YR7wQ7O/RhHvqBigzewKV/syQZQ=
		</data>
		<key>PlugIns/widget-mainExtension.appex/graphite-colors.json</key>
		<data>
		np7EJs3SHyGXbeA+fX/cfEzy480=
		</data>
		<key>PlugIns/widget-mainExtension.appex/graphite-theme.json</key>
		<data>
		LqeVMAoHG+oCR5NyV/+9Hz4QJBM=
		</data>
		<key>PlugIns/widget-mainExtension.appex/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2P+PjoufHmOEDNu336ve6rqLDAM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nELECNjh/DXfP4ElOEfZe7X4XIA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/karat-animation.json</key>
		<data>
		gAGkywTUQ2hFODLtmtC7gEwXP3c=
		</data>
		<key>PlugIns/widget-mainExtension.appex/karat-colors.json</key>
		<data>
		+Mq70YusyQAn+9A4Y1D2F4AMigs=
		</data>
		<key>PlugIns/widget-mainExtension.appex/karat-theme.json</key>
		<data>
		ys3gO/H606017EQix6ZrYbu97yA=
		</data>
		<key>PlugIns/widget-mainExtension.appex/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/wuFqBA3/wO0M0tKH1oW+Qlir9Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/monster-animation.json</key>
		<data>
		aEtpqqeq25BOAsESeZjUYMwK1YI=
		</data>
		<key>PlugIns/widget-mainExtension.appex/monster-colors.json</key>
		<data>
		NItWhVRF0e5NKx61tLhQsS+JVgs=
		</data>
		<key>PlugIns/widget-mainExtension.appex/monster-theme.json</key>
		<data>
		OTuCV/89NlfbPugIWset1pcmTAk=
		</data>
		<key>PlugIns/widget-mainExtension.appex/normal-animation.json</key>
		<data>
		cf3RwXmcWshBcK2KLXyob6vO1j0=
		</data>
		<key>PlugIns/widget-mainExtension.appex/normal-colors.json</key>
		<data>
		8YiWkSvW0zx9OVrCiilifY6iCCQ=
		</data>
		<key>PlugIns/widget-mainExtension.appex/normal-theme.json</key>
		<data>
		E7YQDPKEN6UPLK8vqrW7ynr5N6U=
		</data>
		<key>PlugIns/widget-mainExtension.appex/opal-animation.json</key>
		<data>
		JxA+QKOggC+LfCpsyltQYX8TGX8=
		</data>
		<key>PlugIns/widget-mainExtension.appex/opal-colors.json</key>
		<data>
		lRY8D0TWIc8/rI6FvomnxAdGv/w=
		</data>
		<key>PlugIns/widget-mainExtension.appex/opal-theme.json</key>
		<data>
		W8U9oQe4l5m28/gHjL8ZjtVvpuc=
		</data>
		<key>PlugIns/widget-mainExtension.appex/presstube-animation.json</key>
		<data>
		Jh/2brca/CFAkquNIrJuhQdDC9g=
		</data>
		<key>PlugIns/widget-mainExtension.appex/presstube-colors.json</key>
		<data>
		hRGtshy6vCyOXnkqMXig+f4hvY8=
		</data>
		<key>PlugIns/widget-mainExtension.appex/presstube-theme.json</key>
		<data>
		P8bZldX26ircgfJIKVyDMzuYeK8=
		</data>
		<key>PlugIns/widget-mainExtension.appex/widget-mainExtension</key>
		<data>
		ncM415ic6Pex5JUrWl+P81XZ6TQ=
		</data>
		<key>PlugIns/widget-mainExtension.appex/wireframe-animation.json</key>
		<data>
		oPf7NGvKfp9A/y1DmScXTxiRbzs=
		</data>
		<key>PlugIns/widget-mainExtension.appex/wireframe-colors.json</key>
		<data>
		SkET4REpucuQlvEopWhZgzy3xvY=
		</data>
		<key>PlugIns/widget-mainExtension.appex/wireframe-theme.json</key>
		<data>
		8ji1OdI/yl3KdcG520CtnE7BUCE=
		</data>
		<key>PlugIns/widget-mainExtension.appex/zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HGf0fK/p7N26ga/Y9qcO9p63+AA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Shi/GjyOXaU+CsI6mdntDrdGAW0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+ciEm+qDGp+UZoYEmWF/ETcdQhA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		twc8slLn0q5hstPg5A8z0a+5Rq4=
		</data>
		<key>RevenueCat_RevenueCat.bundle/Info.plist</key>
		<data>
		MVZ+U0wtJQF7b0Wc1vq1FzkY86I=
		</data>
		<key>RevenueCat_RevenueCat.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		uqRDkW3s33psRj8H+jal4YSzsvk=
		</data>
		<key>RevenueCat_RevenueCatUI.bundle/Assets.car</key>
		<data>
		VkivHEN0Sq7H7WpXWg2/23RU0nY=
		</data>
		<key>RevenueCat_RevenueCatUI.bundle/Info.plist</key>
		<data>
		1t40HiElje0wTOPeNb3VuynXqd8=
		</data>
		<key>RevenueCat_RevenueCatUI.bundle/ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IgrDYE7s5Ao1kgXGWhkmE6DtaSw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/background.jpg</key>
		<data>
		cobmQyDEiwBkOm53urv6Z0Jheeg=
		</data>
		<key>RevenueCat_RevenueCatUI.bundle/bg.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1DK4kp//KoPu6EliBCoHJSm3hSg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+0WdXT9iA7rxmekkeFV1CFfUr0M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzfk/vzfrNFlQAV78mNv+sM+cLs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/da.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Tb1M+JlXwha6CASNZN99jg1lCjY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+1wNXbdjC5n5rXHD3ujvnjU2aOw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/el.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			j7zTilv4aawn1JvdeWDti6lfvpY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_AU.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_CA.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_GB.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_US.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/es_419.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rp8pvRolRrgVJOqfZRqqPqT/zB0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/es_ES.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rp8pvRolRrgVJOqfZRqqPqT/zB0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zPSCYzm5sUbOkWRJLBpML1c7bpI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/fr_CA.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fwtMweVYC4tNUSn94QdCECWfosc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/he.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OOwIfBI8gTKKCd6IG0lAxSVfGHI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/hi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XoLnoVnrb8rYzqZkbWn4Y4jmyoM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/hr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v69QiECFoEXKsPyKgwTM6/0+988=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7U3zm8yKWi31CkYV5toBCx2pHfA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/id.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dTay5oBh44GUzpUfM/MYxpycpZk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oq1WnovzEqv8jMG/r3GzZdYL9bg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mhl5cJsbiu8E630s+IBtqg/rGS8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/kk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p+pJpe7nUaOnu2TSmYQ/7On9YrI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xMJ1BrfbetANxc5M2m4JLDeGvBY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ms.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GJ013SGsBV29jWSP6lMoiNKrlsE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rj/1cU0FueMctW5Idlc4DffFL4A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/no.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ohd0nuuHYRbQH5m7Iav1VkCBIhE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Y7VJO0AodP4+HUtD99S53Fo1eE0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/pt_BR.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ouomTBJZPP2rz7ePbMzBtD4zVCk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/pt_PT.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ouomTBJZPP2rz7ePbMzBtD4zVCk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XdJv/J/wOKxJMc4m1bBz6QiG8xA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gwj1mlzWt5aADE1iE+9KDdkAzhw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TfCNaaj825wCwvrXLJ8bozt6Mxo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5stHn6nvQv0kKmzer+shwoxHgVw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/th.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8fWP6ZGNpnRL2mozleLAHZu+asE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AyHk7CPwbQ/0lK4wnv+2YTBsYmE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/uk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uXBx8YT+kSTOMpZhEST69s90wIc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/vi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9b8184pr3yNjMqWMKU8oyGTuGjE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/zh_Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XfpxFjPoJ1mnulmHM595TUxkDfs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/zh_Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0DPdeshPj2dIZd5BIzKTZhyQmgg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SC_Info/Manifest.plist</key>
		<data>
		8bluJJg6Z1LeDK/8chpPtnnQBIk=
		</data>
		<key>SimpleCore_SimpleCore.bundle/Assets.car</key>
		<data>
		QuY1SRVaDhGUKRnTDG7pGEGtTGk=
		</data>
		<key>SimpleCore_SimpleCore.bundle/Info.plist</key>
		<data>
		icomyb6ys7NNbVOtazV8NyG9qCo=
		</data>
		<key>SimpleCore_SimpleCore.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zzQ2NXhAgc2EwqNSrghGyx75Zsk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CbHT+zxH4kLofQyjzZqOn9U/XuU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PEPEDUKRjYVTc9rZmKejmxuAu3M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/explode-open.ahap</key>
		<data>
		VAAR35KfKPUX5E+tFwedxZlNJUo=
		</data>
		<key>SimpleCore_SimpleCore.bundle/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qwFbqEe7kyM+4s62SMsPOhNdEUo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qSuDCVKNGwFjGLeFtsK9eMCV4a8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7HVCabV9EkQSS02uDL+rXCxyXOY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i91b5xMeECm4CpmeJQpIHYXuM9Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PWYvfp57Fjn115z1QgQ2u3WXXd0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Kz6CRitjor6eDbhbEEqNpb5J7rE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>andy-animation.json</key>
		<data>
		4jcmNT2NgfK5sDTc6WBk14nGqiU=
		</data>
		<key>andy-colors.json</key>
		<data>
		1q9gV9plEJkY1/VBOwPlOEpsbzQ=
		</data>
		<key>andy-theme.json</key>
		<data>
		Y+w8FBhrkhcbFJtP937+cb3eGyQ=
		</data>
		<key>andy.scnassets/Main.scn</key>
		<data>
		i1ywSI0I8vCuw/IjOV9i2SVdA4s=
		</data>
		<key>andy.scnassets/Models.scn</key>
		<data>
		iifdbn/SOqTGxyGPFKH/HDvuse4=
		</data>
		<key>andy.scnassets/Numbers.scn</key>
		<data>
		dJ5rM6TDU/NKlhcmviAk2llzDvM=
		</data>
		<key>andy.scnassets/Particles.scn</key>
		<data>
		hyl4fC5E5gLh4v321KxeBJbz+e4=
		</data>
		<key>app-intro.caf</key>
		<data>
		68yK1sK/HEbBffp4J29jDPlpTwE=
		</data>
		<key>base1-animation.json</key>
		<data>
		JkEC572cv5WTKSHKxpNuo82gNYI=
		</data>
		<key>base1.scnassets/Animation-CameraShake.scn</key>
		<data>
		V5YFhtWfsymZgfqvbWpQ/Ye4yds=
		</data>
		<key>base1.scnassets/Animation-bolt.scn</key>
		<data>
		hcISiAIXHUTfcriIpXKqZ2VEbxk=
		</data>
		<key>base1.scnassets/Animation-cloud-in.scn</key>
		<data>
		m5oCCkKCeEo5vFPRYynxOh5y9DE=
		</data>
		<key>base1.scnassets/Animation-cloud-out.scn</key>
		<data>
		CetYuTvlGZeZ5kYZcXZ9Jqa+Oi8=
		</data>
		<key>base1.scnassets/Animation-cloud.scn</key>
		<data>
		OuDAACkyhoAAtCM/ZkqrlF0OS04=
		</data>
		<key>base1.scnassets/Animation-fog.scn</key>
		<data>
		HqnRa1RpIhjAiKrXBjq4r7RCyts=
		</data>
		<key>base1.scnassets/Animation-particle-pop.scn</key>
		<data>
		lvLBFu6hVqAv1DCpXTxefW/WJX4=
		</data>
		<key>base1.scnassets/Animation-pop.scn</key>
		<data>
		2/KZMHn0FP1jhyi6/nxy3DQsL4Y=
		</data>
		<key>base1.scnassets/Animation-sun-in.scn</key>
		<data>
		OsRDr+nTu5ykDopYl6HeKTq3sVw=
		</data>
		<key>base1.scnassets/Animation-sun-out.scn</key>
		<data>
		yRyAiR2wCEocMipm5La2WS0um6o=
		</data>
		<key>base1.scnassets/Particles.scn</key>
		<data>
		7+WgHXh/567/RY8upmlkDih6J6g=
		</data>
		<key>base1.scnassets/precip-particle-128.heic</key>
		<data>
		iKRpaoh23H3ZB7iA0+akkm3OOts=
		</data>
		<key>base1.scnassets/precip-particle-16.heic</key>
		<data>
		+nUjFBklhS3TXrvtcn1fS588r9k=
		</data>
		<key>base1.scnassets/precip-particle-32.heic</key>
		<data>
		aNkrAzVhB8MO3YEDBsSfM2f82fk=
		</data>
		<key>base1.scnassets/precip-particle-64.heic</key>
		<data>
		ddlSrGNn+NB6lA/NPnoo9tpoGSY=
		</data>
		<key>base1Numbers.scnassets/Numbers.scn</key>
		<data>
		tTBVjGQwY64yC4QVE8JSgQqTfng=
		</data>
		<key>bricks-animation.json</key>
		<data>
		P1+SaemHjcyujmgUm1t/FwGxYWk=
		</data>
		<key>bricks-colors.json</key>
		<data>
		nyzxG2TyHd5NtldIqNnUQM/aon8=
		</data>
		<key>bricks-theme.json</key>
		<data>
		VzPY0XwcukSTzgAF+CiU+gZsnxw=
		</data>
		<key>bricks.scnassets/Main.scn</key>
		<data>
		y1CilpdudlMNRec8DY8gc7XXxKg=
		</data>
		<key>bricks.scnassets/Models.scn</key>
		<data>
		0DI8LAkP8EhVxpwjjUIDuJyJgcY=
		</data>
		<key>bricks.scnassets/Numbers.scn</key>
		<data>
		1Sp7cIb6hnI25+zLS2Vc7Ht862c=
		</data>
		<key>bricks.scnassets/Particles.scn</key>
		<data>
		9I4gIdHCSac6CG24wuh7YiPfH70=
		</data>
		<key>cedar-animation.json</key>
		<data>
		Jd2PtxGD1MmcLk0cGgW9I9RpN4w=
		</data>
		<key>cedar-colors.json</key>
		<data>
		nAOcGGMgqJqL5iNos2AutWeV3nc=
		</data>
		<key>cedar-theme.json</key>
		<data>
		aHOIurzBqkzl3ksBfe8gS5fi1d8=
		</data>
		<key>cedar.scnassets/Main.scn</key>
		<data>
		8Zu0579W99tgcdbbFFYVLVEOPLU=
		</data>
		<key>cedar.scnassets/Models.scn</key>
		<data>
		iBiiWh+M6xzvohi81IELZNg98Xk=
		</data>
		<key>cedar.scnassets/Numbers.scn</key>
		<data>
		rH6CJJuFzZO9bGpUA4F0ooNnKbA=
		</data>
		<key>cedar.scnassets/Particles.scn</key>
		<data>
		Vn3SDgbUN/aA/B+eUK56WuJfxZs=
		</data>
		<key>cedar.scnassets/eran-day-fog.heic</key>
		<data>
		x/mQBhpD2ygv91wTDKLN3N49OXI=
		</data>
		<key>cedar.scnassets/eran-night-fog.heic</key>
		<data>
		Bs/036WWOH8GNk6d/aXGBwz7HVw=
		</data>
		<key>cedar.scnassets/eran-worm.png</key>
		<data>
		7FDjJdJansh9o0HVsiRtHDvyqk8=
		</data>
		<key>chroma-animation.json</key>
		<data>
		RUupW9F8QlvVdsAGvZm4Q+D20Ko=
		</data>
		<key>chroma-colors.json</key>
		<data>
		dtx1Kc3YLWxPaHUsznENRU9tLcQ=
		</data>
		<key>chroma-theme.json</key>
		<data>
		stGwudiXtOLqJl1VTczFzTGE+ZU=
		</data>
		<key>chroma.scnassets/Main.scn</key>
		<data>
		5Z8wdjaPcU9yFrPWNW4/lzcRAck=
		</data>
		<key>chroma.scnassets/Models.scn</key>
		<data>
		EDpcEVUc/7CMfYjGpsT/QLqZFow=
		</data>
		<key>chroma.scnassets/Numbers.scn</key>
		<data>
		TNLh2nk7Ukx7Xi7DQFN55ANCLWE=
		</data>
		<key>chroma.scnassets/Particles.scn</key>
		<data>
		EFT64sq4FVl7+9ZS4tizQHqIiHE=
		</data>
		<key>chroma.scnassets/oilslick-rainbow-8.ktx</key>
		<data>
		rNewckxfsOPDSv6BhgbyJgsoCow=
		</data>
		<key>cloudburst-title.json</key>
		<data>
		V6QxkX06eJUy43WJJ4ZCskY3E2M=
		</data>
		<key>de.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Et+yk0iKrrascmsp1VYwUwb4aHY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LTDON6YEF7ST70ahhzpRlX+dllo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>default.metallib</key>
		<data>
		dsXAdWS6cd9GJ7vas5TsxhoFuDg=
		</data>
		<key>depth-animation.json</key>
		<data>
		i6AHSjdO9nTgEpGKiYpKnCwd47E=
		</data>
		<key>depth-colors.json</key>
		<data>
		PqAdyFaK6bIPty7YiB1cXxv7owc=
		</data>
		<key>depth-theme.json</key>
		<data>
		yZ1JyHEvjpSoiYQc9jN7FD+49CA=
		</data>
		<key>depth.scnassets/Main.scn</key>
		<data>
		BrvyBkR9/vEo1Jf0XwyDua89w9A=
		</data>
		<key>depth.scnassets/Models.scn</key>
		<data>
		SJBpfxO9RnURG4lsYrlTFamj/Ik=
		</data>
		<key>depth.scnassets/Numbers.scn</key>
		<data>
		HvUZQ/SLKOk93mCvX9E8DYnjypc=
		</data>
		<key>depth.scnassets/Particles.scn</key>
		<data>
		zkcwo0Qk+stak39qxKMUNzC1SAY=
		</data>
		<key>detailed-enter-1.caf</key>
		<data>
		c2JMnEGV8Gbk8qnjnukHmoUsCnQ=
		</data>
		<key>detailed-exit-1.caf</key>
		<data>
		MFWyiESvY04BB6mME+Wl/LIYlY4=
		</data>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			M2SSnM+6MeTDWE5Q6p1bQBVsuE8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/EHBdA5VdycM64mhhD4lJJsUVP4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>environment-13.png</key>
		<data>
		tRZts3Mbk9wSvWjulvuJgkhvSUg=
		</data>
		<key>es.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			l7qkTcmA0PTxu6o11poUXHig7Ak=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mSmC+D9j02gAPc1cwywbssQiBKQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>explode-close.ahap</key>
		<data>
		TQy52pumlxm1scYQHd6eum07DIY=
		</data>
		<key>explode-enter-1.caf</key>
		<data>
		3rdngWVM5LA84UFjlWca4WPdhiM=
		</data>
		<key>explode-open.ahap</key>
		<data>
		VAAR35KfKPUX5E+tFwedxZlNJUo=
		</data>
		<key>fr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+go7sbv5QjGL7m4u42RMrw1kDB8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cVBkX8o0wGEzHC1mqOzEvPkmlLU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>graphite-animation.json</key>
		<data>
		YR7wQ7O/RhHvqBigzewKV/syQZQ=
		</data>
		<key>graphite-colors.json</key>
		<data>
		np7EJs3SHyGXbeA+fX/cfEzy480=
		</data>
		<key>graphite-theme.json</key>
		<data>
		LqeVMAoHG+oCR5NyV/+9Hz4QJBM=
		</data>
		<key>graphite.scnassets/Main.scn</key>
		<data>
		LA1AKuAHT6pD/0akIJp83KH1vLI=
		</data>
		<key>graphite.scnassets/Models.scn</key>
		<data>
		6NLjOxCgx72BNWjbn9sf7rsX5Ck=
		</data>
		<key>graphite.scnassets/Numbers.scn</key>
		<data>
		cC0LQlLYrXVpTgqJZxMTMRjkKFE=
		</data>
		<key>graphite.scnassets/Particles.scn</key>
		<data>
		35J9Rn89XIH5jJC21gQQRJc/MoI=
		</data>
		<key>it.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4vM+KD0cvtYf4JyLml1D60EXViA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2P+PjoufHmOEDNu336ve6rqLDAM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2C7ii5Kv4vKxRjCjf3Kp5BZ1vAY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nELECNjh/DXfP4ElOEfZe7X4XIA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>karat-animation.json</key>
		<data>
		gAGkywTUQ2hFODLtmtC7gEwXP3c=
		</data>
		<key>karat-colors.json</key>
		<data>
		+Mq70YusyQAn+9A4Y1D2F4AMigs=
		</data>
		<key>karat-theme.json</key>
		<data>
		ys3gO/H606017EQix6ZrYbu97yA=
		</data>
		<key>karat.scnassets/Main.scn</key>
		<data>
		XhWuDhJYOijrhC5FCDFR94XwOEM=
		</data>
		<key>karat.scnassets/Models.scn</key>
		<data>
		Z18C/Ep2cz7DgLr1ZaELLDfsg9Y=
		</data>
		<key>karat.scnassets/Numbers.scn</key>
		<data>
		qdWExAJjhOkis8H+FFQJzdmQcxg=
		</data>
		<key>karat.scnassets/Particles.scn</key>
		<data>
		j19DBwsvvPHh3ErSQmw3BnGgUrg=
		</data>
		<key>karat.scnassets/gold-dim.heic</key>
		<data>
		8UPb0hBIgCf61XXfWwwBPWz8asw=
		</data>
		<key>karat.scnassets/terrazzo-mask.heic</key>
		<data>
		1tRCklUlUxa2RZ8bEskhPDO8IME=
		</data>
		<key>ko.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			w5J6bqb2u5ubRlejwbWpP9GnwHg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/wuFqBA3/wO0M0tKH1oW+Qlir9Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>membership.scnassets/back-sticker-reflective.heic</key>
		<data>
		jamLeUp1+TQE6e939lca+Ehovrk=
		</data>
		<key>membership.scnassets/black-reflective-6.heic</key>
		<data>
		1oZLz+l7q44MOgZ9OuKVVUs3p+A=
		</data>
		<key>membership.scnassets/coin-normal.heic</key>
		<data>
		OoVCKjAt+VKhb3wlV9CXne15kW0=
		</data>
		<key>membership.scnassets/environment-13.heic</key>
		<data>
		yxj82kveuGE49NrF4yrXsGfrucQ=
		</data>
		<key>membership.scnassets/gold-3.heic</key>
		<data>
		s1kBIuvRszyM2dVIaUzB7aUv8CE=
		</data>
		<key>membership.scnassets/member-card.scn</key>
		<data>
		lTTBorixMizq32hjN1FMIeEFRhk=
		</data>
		<key>membership.scnassets/particle-sparkle-64.heic</key>
		<data>
		y2CX2EPFnvva/8exJ9kvY1fd2uc=
		</data>
		<key>membership.scnassets/patron-reflective-2.heic</key>
		<data>
		D1sEpw2WetMYijwgaWyBKWDaUn8=
		</data>
		<key>membership.scnassets/unlock.caf</key>
		<data>
		2ZB+fUN1SPtlV9JA55lUzA8a9Fc=
		</data>
		<key>menu-close-1.caf</key>
		<data>
		Xw598IULhI4yrLlJJ7rsGfo4Sk0=
		</data>
		<key>menu-open-1.caf</key>
		<data>
		rusJyBRbKCB+AbouBTUltwQ1/M8=
		</data>
		<key>model-change-1.caf</key>
		<data>
		Hzbaq2zoQ5QESa1bB5OrCWNzY3Y=
		</data>
		<key>model-change-2.caf</key>
		<data>
		Gabb5Iej/gM1AKV6UThji/QEveM=
		</data>
		<key>model-change-3.caf</key>
		<data>
		DxJ2TioIZy8nLa62cLJvQmWzFmc=
		</data>
		<key>monster-animation.json</key>
		<data>
		aEtpqqeq25BOAsESeZjUYMwK1YI=
		</data>
		<key>monster-colors.json</key>
		<data>
		NItWhVRF0e5NKx61tLhQsS+JVgs=
		</data>
		<key>monster-theme.json</key>
		<data>
		OTuCV/89NlfbPugIWset1pcmTAk=
		</data>
		<key>monster.scnassets/Main.scn</key>
		<data>
		ogk4RUnke1BGkVLy0ATYDOD4mQ4=
		</data>
		<key>monster.scnassets/Models.scn</key>
		<data>
		0Ba/GPX2xxNyXKPxbv8KGAlhBvA=
		</data>
		<key>monster.scnassets/Numbers.scn</key>
		<data>
		aWV3BDPe7CbUgmY4Y1+VbLUaUE4=
		</data>
		<key>monster.scnassets/Particles.scn</key>
		<data>
		DUwZpvjGFYfm0RJaUCyawgjpNlM=
		</data>
		<key>moon-tapped-1.caf</key>
		<data>
		OPziTjYzgq109RTL8UEp2E3pvy8=
		</data>
		<key>moon-tapped-2.caf</key>
		<data>
		6mFHhVZiroGI6Lw2AH8KAZXoZTc=
		</data>
		<key>moon-tapped-3.caf</key>
		<data>
		ZO5OUREEO++Kagw9uV8dSbc0KI8=
		</data>
		<key>moon-tapped-4.caf</key>
		<data>
		SL5N04Q17i7eGhR2u5g4+jMY4so=
		</data>
		<key>moon-tapped-5.caf</key>
		<data>
		dpUUDGWveOVFg5GXWvGJmkVPJt0=
		</data>
		<key>noise-normal-256.png</key>
		<data>
		XXXN6dGFLmwHWB3noW2ZHdckl4I=
		</data>
		<key>noise-perlin-256.png</key>
		<data>
		Qqy+1iZEZJOv0vldaeyJbqKdRWw=
		</data>
		<key>normal-animation.json</key>
		<data>
		cf3RwXmcWshBcK2KLXyob6vO1j0=
		</data>
		<key>normal-colors.json</key>
		<data>
		8YiWkSvW0zx9OVrCiilifY6iCCQ=
		</data>
		<key>normal-theme.json</key>
		<data>
		E7YQDPKEN6UPLK8vqrW7ynr5N6U=
		</data>
		<key>normal.scnassets/Main.scn</key>
		<data>
		9FsF/YAGaMLP1AHnki9fYyXBvMc=
		</data>
		<key>normal.scnassets/Models.scn</key>
		<data>
		QJVwltjr0CLuwjWeO3eqbo9Hc7k=
		</data>
		<key>normal.scnassets/Numbers.scn</key>
		<data>
		9bT9ZC0qb30QXWc3NcMf7+CZtQI=
		</data>
		<key>normal.scnassets/Particles.scn</key>
		<data>
		gaiGR0kKYwBmRcs7t/qrD3EyD1U=
		</data>
		<key>opal-animation.json</key>
		<data>
		JxA+QKOggC+LfCpsyltQYX8TGX8=
		</data>
		<key>opal-colors.json</key>
		<data>
		lRY8D0TWIc8/rI6FvomnxAdGv/w=
		</data>
		<key>opal-theme.json</key>
		<data>
		W8U9oQe4l5m28/gHjL8ZjtVvpuc=
		</data>
		<key>opal.scnassets/Main.scn</key>
		<data>
		yebSzAR3TCjLfr9FMJso4Zx+txM=
		</data>
		<key>opal.scnassets/Models.scn</key>
		<data>
		q0eSE7RKQrASQluW7oHz+XFcjVc=
		</data>
		<key>opal.scnassets/Numbers.scn</key>
		<data>
		dyWirefEnHf7x4BueemkNz8jrI4=
		</data>
		<key>opal.scnassets/Particles.scn</key>
		<data>
		aGrGF3llV4T9b/olSOuFZR6TLHY=
		</data>
		<key>pieday-crust.heic</key>
		<data>
		Jp6B07R/dnmAl4LEssmet73/HLE=
		</data>
		<key>pieday-filling.heic</key>
		<data>
		IekqFYSfMIldu2LVvqGt8njjVtU=
		</data>
		<key>presstube-animation.json</key>
		<data>
		Jh/2brca/CFAkquNIrJuhQdDC9g=
		</data>
		<key>presstube-colors.json</key>
		<data>
		hRGtshy6vCyOXnkqMXig+f4hvY8=
		</data>
		<key>presstube-theme.json</key>
		<data>
		P8bZldX26ircgfJIKVyDMzuYeK8=
		</data>
		<key>presstube.scnassets/0-04-13-black.ktx</key>
		<data>
		EjePHxKiw+FQ2V7D9Yb1dMMRQKQ=
		</data>
		<key>presstube.scnassets/0-04-13.ktx</key>
		<data>
		kntj13+5eswQBuu0miQZJCFDj2g=
		</data>
		<key>presstube.scnassets/1-04-13-black.ktx</key>
		<data>
		teuex54bKtZSP6cWrzpxQiPZ27E=
		</data>
		<key>presstube.scnassets/1-04-13.ktx</key>
		<data>
		TDa84alcKy0kFzyZi/n3h+xOF04=
		</data>
		<key>presstube.scnassets/2-04-13-black.ktx</key>
		<data>
		H12WhzmWCmyLJKYzlYLy5ItY4iM=
		</data>
		<key>presstube.scnassets/2-04-13.ktx</key>
		<data>
		VKTgat3E9lDAYZbc4+JCNWHEvN4=
		</data>
		<key>presstube.scnassets/3-04-13-black.ktx</key>
		<data>
		eI4Ee+J19KEpPGerIzDXnudraZA=
		</data>
		<key>presstube.scnassets/3-04-13.ktx</key>
		<data>
		DDZPZzZs8HnsoUI8VKBCZqITqGw=
		</data>
		<key>presstube.scnassets/4-04-13-black.ktx</key>
		<data>
		L90k627K7cPguk1ClPkAjAWgWAE=
		</data>
		<key>presstube.scnassets/4-04-13.ktx</key>
		<data>
		PClVaFrA+GCHuxj4DrAbDBmgeVE=
		</data>
		<key>presstube.scnassets/5-04-13-black.ktx</key>
		<data>
		/hPJD1hqH8s7Au+Sdx7KcfmoxCM=
		</data>
		<key>presstube.scnassets/5-04-13.ktx</key>
		<data>
		7BIyy+zKWTN7nZMFuaCDaDYpAqM=
		</data>
		<key>presstube.scnassets/6-04-13-black.ktx</key>
		<data>
		ch73Ef1hV8OG2m1qLmsvhQdDVqs=
		</data>
		<key>presstube.scnassets/6-04-13.ktx</key>
		<data>
		mZY9rMtHyfIQnkHRl8fSoednCJw=
		</data>
		<key>presstube.scnassets/7-04-13-black.ktx</key>
		<data>
		qoXtF/1bPQ+1KOutvS2T3DugYFk=
		</data>
		<key>presstube.scnassets/7-04-13.ktx</key>
		<data>
		HxWDsIMZy8XR/CkeJgrwS/eoReA=
		</data>
		<key>presstube.scnassets/8-04-13-black.ktx</key>
		<data>
		1eyQmJpfMxeollGVQlUtf243oWY=
		</data>
		<key>presstube.scnassets/8-04-13.ktx</key>
		<data>
		L02ZmtKoT/wlIB2anFtyILCwzmo=
		</data>
		<key>presstube.scnassets/9-04-13-black.ktx</key>
		<data>
		8jib6lNzU4DC6gDSgjp92YHdwyE=
		</data>
		<key>presstube.scnassets/9-04-13.ktx</key>
		<data>
		ljsTHvpxFaTJUkPlPA95okIfFjc=
		</data>
		<key>presstube.scnassets/Main.scn</key>
		<data>
		FJYmr97qzVionip31f9x0ZftXPc=
		</data>
		<key>presstube.scnassets/Models.scn</key>
		<data>
		0jEIAApMXf2Gb2B87grpLyUb9Rs=
		</data>
		<key>presstube.scnassets/Numbers.scn</key>
		<data>
		LhyLueWOxw9opuWM+xMd/KIwt/c=
		</data>
		<key>presstube.scnassets/Particles.scn</key>
		<data>
		nVMeaDq7NA/6Ev7Ww46iirdaKAk=
		</data>
		<key>presstube.scnassets/comma-04-26-black.ktx</key>
		<data>
		q2A7GaLLfib0Zu8xlUz6cSZfBys=
		</data>
		<key>presstube.scnassets/comma-04-26.ktx</key>
		<data>
		jIy4YLed2p1Uk4kBIzn2r59XqeU=
		</data>
		<key>presstube.scnassets/minus-04-26-black.ktx</key>
		<data>
		DfU1K6WGKmR49QaTACxqpieCbSE=
		</data>
		<key>presstube.scnassets/minus-04-26.ktx</key>
		<data>
		1Ul7qLEkBCzl2TD+EXuyc0gO3AA=
		</data>
		<key>presstube.scnassets/perc-04-26-black.ktx</key>
		<data>
		gJgmW9Mj5JHZe+zsCelyLNh+MjE=
		</data>
		<key>presstube.scnassets/perc-04-26.ktx</key>
		<data>
		5nE/buAnySgcHp2JAHvs9bNX6k4=
		</data>
		<key>presstube.scnassets/period-04-26-black.ktx</key>
		<data>
		1OcQxNWlurJmELq7rhU2IGvpRw8=
		</data>
		<key>presstube.scnassets/period-04-26.ktx</key>
		<data>
		eZyB56uzc2sc3bYZR9PqMl6cyc8=
		</data>
		<key>presstube.scnassets/plus-04-26-black.ktx</key>
		<data>
		vviEI1qPeEmGrhQdqjnWPNVovnE=
		</data>
		<key>presstube.scnassets/plus-04-26.ktx</key>
		<data>
		jLhKuBE9JrgVf8fqdTcOrmr7h9E=
		</data>
		<key>presstube.scnassets/sun-04-21-22-black.ktx</key>
		<data>
		aR/D2u4k3BlINiLca3IU1PEKr4g=
		</data>
		<key>presstube.scnassets/sun-04-21-22.ktx</key>
		<data>
		lA7lTsFVGmP8aPsGLgJBgiRySH0=
		</data>
		<key>shape-tap-1.caf</key>
		<data>
		f7S6R5eI0Vn1lI3sM5OnvK9OXlU=
		</data>
		<key>shape-tap-2.caf</key>
		<data>
		98Po0gqS1DYf8DQIOp8XKmjYzYU=
		</data>
		<key>shape-tap-3.caf</key>
		<data>
		kc7kqesyhi7gL8lt826TZUldgok=
		</data>
		<key>shape-tap-4.caf</key>
		<data>
		aTzqP/0MODGS8dLdCddJE96x2HI=
		</data>
		<key>shape-tap-5.caf</key>
		<data>
		/vlmI3zugS3yYeextsMNK5X7Aqc=
		</data>
		<key>shape-tap-6.caf</key>
		<data>
		fOdpmimt4lkLb/R+VUd2Cp6WUQg=
		</data>
		<key>shape-tap-7.caf</key>
		<data>
		7Qjjk1HfsGq7GzVRABZR527NFGY=
		</data>
		<key>shape-tap-8.caf</key>
		<data>
		ufyRPHgpfnl1+4bQGOTlSTdU13s=
		</data>
		<key>shared.scnassets/Events.scn</key>
		<data>
		JVeX614gqeD7IKuQ00Z3bFmsLOY=
		</data>
		<key>shirt003-texture-1024.heic</key>
		<data>
		CgK+4y13xrHpjJI8cc8aJ23dz3E=
		</data>
		<key>shop.scnassets/shirt001-texture-1024.png</key>
		<data>
		AqIdM44/qLWuLbSmBbUDmWwx1EY=
		</data>
		<key>shop.scnassets/shirt002-brown-1024.heic</key>
		<data>
		f8AWbWtqqa9z1DIhfkDlRLc3t48=
		</data>
		<key>shop.scnassets/shop-items.scn</key>
		<data>
		lnljj3qhH74txLNLN/Hoj3f0BWE=
		</data>
		<key>spin-fast-1.caf</key>
		<data>
		F7jJp9QnbsVBGoyJkLQdQG+MSDQ=
		</data>
		<key>spin-fast-2.caf</key>
		<data>
		CYpmFUhuc49R0OMqysYhNwFSNqY=
		</data>
		<key>spin-fast-3.caf</key>
		<data>
		78XRcaa7hEdvzYNRp439dTtt/Y4=
		</data>
		<key>spin-fast-4.caf</key>
		<data>
		DfzsOUSsQaze1vO9UEApWHsPykU=
		</data>
		<key>spin-fast-5.caf</key>
		<data>
		HLZvcnmxLqggzr/VGzx5GUNKgKc=
		</data>
		<key>spin-fast-6.caf</key>
		<data>
		d04lnt0T31dMbsKR6DY1YbWTLUM=
		</data>
		<key>spin-slow-1.caf</key>
		<data>
		cd4Wk4ATor1Ej4RaOzodGy1y75A=
		</data>
		<key>spin-slow-10.caf</key>
		<data>
		MqO7l8o/iDehwpOlqTnyRHL2mL0=
		</data>
		<key>spin-slow-2.caf</key>
		<data>
		1hX1O21bFkxBwr+TH0zHUSWXXyk=
		</data>
		<key>spin-slow-3.caf</key>
		<data>
		qNbQprkWcYlBDi38cd4OqvC/5g4=
		</data>
		<key>spin-slow-4.caf</key>
		<data>
		/seiNeXrv/vDMQtN3rot+PDSJJg=
		</data>
		<key>spin-slow-5.caf</key>
		<data>
		6/Aoae4dMg1fW4L1Xqmj5+5ZegE=
		</data>
		<key>spin-slow-6.caf</key>
		<data>
		YV+zn8GQtpSoMOmAQD5N3/JFALM=
		</data>
		<key>spin-slow-7.caf</key>
		<data>
		KWJgYDz4Zavr4bejryEN/DMpvGg=
		</data>
		<key>spin-slow-8.caf</key>
		<data>
		B7sPvcKHFoRh2fdBsPbKjA8TJyQ=
		</data>
		<key>spin-slow-9.caf</key>
		<data>
		lGdpQWZlmP3gv/Ggh/1e0t5wr9k=
		</data>
		<key>sun-tapped-1.caf</key>
		<data>
		ODMbj6C1YzyD7f+D3KbTyvaGLUc=
		</data>
		<key>sun-tapped-2.caf</key>
		<data>
		+SJkLEhYE3TuCemb5sVuCSsNG+4=
		</data>
		<key>sun-tapped-3.caf</key>
		<data>
		EtVUToUwJ1Bhuh60s303D7s4wWc=
		</data>
		<key>sun-tapped-4.caf</key>
		<data>
		moECo92NP/6L52pAGj3eeVZu1Ug=
		</data>
		<key>sun-tapped-5.caf</key>
		<data>
		j+BehnQsxIzo5W/4hcpCukI1jV8=
		</data>
		<key>sun-tapped-6.caf</key>
		<data>
		w4pi1fFgHlGBpQyBVuuPi1ZaWhY=
		</data>
		<key>timer-pass-center-1.caf</key>
		<data>
		m8bLn49O/FEOgtln6zGCZKV4Bgo=
		</data>
		<key>ui-day-select-1.caf</key>
		<data>
		jFNNao//I+ldCTBIHi3a439btK0=
		</data>
		<key>ui-day-select-2.caf</key>
		<data>
		LMQnOUoAglwqXpMJf3M4nt+rd5A=
		</data>
		<key>ui-day-select-3.caf</key>
		<data>
		FnZRH4FfWTLDA7V5d1ppd9e8gAE=
		</data>
		<key>ui-day-select-4.caf</key>
		<data>
		Xy2CaA74prt/88AenGQB2WKdvL8=
		</data>
		<key>ui-day-select-5.caf</key>
		<data>
		QE06/BTC/0fYFO8d/qIMm5xmLF4=
		</data>
		<key>ui-day-select-6.caf</key>
		<data>
		3HMAqNgA9F5e3+uYCYpFfT1mMpA=
		</data>
		<key>ui-day-select-7.caf</key>
		<data>
		qx217DcjNd6yFrSlr59Qhvsmla8=
		</data>
		<key>ui-menu-swipe-down-1.caf</key>
		<data>
		VUacGItmK0laQTodgXVmKsW6FZQ=
		</data>
		<key>ui-menu-swipe-up-1.caf</key>
		<data>
		i1Ugl+KoNyMSFIHyBm9iSPcRYg8=
		</data>
		<key>ui-time-scroll-click-1.caf</key>
		<data>
		3xPkKGFmDJa56XDnrEeFx+/7M0o=
		</data>
		<key>ui-time-scroll-click-2.caf</key>
		<data>
		X/gimLmmj/xQ6RLGZANqJn5mFwk=
		</data>
		<key>ui-time-scroll-click-3.caf</key>
		<data>
		JOoPNDx4Bh+O7OOBdTSwxnpVpG8=
		</data>
		<key>ui-time-scroll-click-4.caf</key>
		<data>
		M3q/QP1B8W89F1QXWdu5oNYW5Vo=
		</data>
		<key>ui-time-scroll-click-5.caf</key>
		<data>
		2Wc3CHhMz8Hr29x+kWel/EE0Vmc=
		</data>
		<key>ui-time-scroll-click-6.caf</key>
		<data>
		K+230SQnQnc0q2SYNlf+xVeNtAI=
		</data>
		<key>weather-ambience-clear.caf</key>
		<data>
		rTkE/vQjLJkViPu2xtTAi1wcHDw=
		</data>
		<key>weather-ambience-cloudy.caf</key>
		<data>
		fapnIRc6vToyMiw5gI3fU1MqJnA=
		</data>
		<key>weather-ambience-icepellets.caf</key>
		<data>
		6UQi+UGACqe/f6SHtD8ifClctcQ=
		</data>
		<key>weather-ambience-mostlyClear.caf</key>
		<data>
		KAXf3Ojgtwx5lX50mB6mfFfGcJ0=
		</data>
		<key>weather-ambience-rain.caf</key>
		<data>
		GpZjMDKzuJXVcGbm7EO9QgaclHI=
		</data>
		<key>weather-ambience-rainLight.caf</key>
		<data>
		4FRJSNsf5uoS+ioqbvXstJizssA=
		</data>
		<key>weather-ambience-snow.caf</key>
		<data>
		869B40fasl5ehl1TGRFvcTB/Ojs=
		</data>
		<key>weather-ambience-thunderstorm.caf</key>
		<data>
		7V29LjXV38k5AjqWZ0pU40Szo/Y=
		</data>
		<key>weather-sting-clear.caf</key>
		<data>
		dbNSl2qhtMSs05yj0oavjQZDv9M=
		</data>
		<key>weather-sting-cloudy.caf</key>
		<data>
		OyYx9Qu20QmERHv7FWlSI1YAAAQ=
		</data>
		<key>weather-sting-fog.caf</key>
		<data>
		Ktmfxx7x506mJvejBnnG4S8UNZg=
		</data>
		<key>weather-sting-heavysnow.caf</key>
		<data>
		vmmthv1lIKlnDTT9uEQmL6lgmkw=
		</data>
		<key>weather-sting-icepellets.caf</key>
		<data>
		2s5pc98i5d8VcAy+NtCkx8XxAHo=
		</data>
		<key>weather-sting-lightrain.caf</key>
		<data>
		XcvG1oHs806kHsR2/oIoazcF/bo=
		</data>
		<key>weather-sting-mostlyclear.caf</key>
		<data>
		kjHCdM2GHnKr/2lMyN9kvVYuVuU=
		</data>
		<key>weather-sting-mostlycloudy.caf</key>
		<data>
		OGX3gNfAgZyRrDh3orDkNzohWVI=
		</data>
		<key>weather-sting-partlycloudy.caf</key>
		<data>
		Wmw44szl9ZbWomsprpc+vpzQggQ=
		</data>
		<key>weather-sting-rain.caf</key>
		<data>
		LlX7p6DBDz4UTiG0kG46A64BE04=
		</data>
		<key>weather-sting-smoke.caf</key>
		<data>
		Cj+WmuLLQmz9ox74rWrRYhX6DHI=
		</data>
		<key>weather-sting-snow.caf</key>
		<data>
		Fan8SALXHFuU7DfRQE3/ky4CUjc=
		</data>
		<key>weather-sting-thunder.caf</key>
		<data>
		VjwW3WNEuBr2B5JdLsFp6FLOyRg=
		</data>
		<key>wireframe-animation.json</key>
		<data>
		oPf7NGvKfp9A/y1DmScXTxiRbzs=
		</data>
		<key>wireframe-colors.json</key>
		<data>
		SkET4REpucuQlvEopWhZgzy3xvY=
		</data>
		<key>wireframe-theme.json</key>
		<data>
		8ji1OdI/yl3KdcG520CtnE7BUCE=
		</data>
		<key>wireframe.scnassets/Main.scn</key>
		<data>
		oYmB5lS518lfCPNkPbWPNn2/p1s=
		</data>
		<key>wireframe.scnassets/Models.scn</key>
		<data>
		Hm1J65Bd9O3AZz9KwKro0eUmDIw=
		</data>
		<key>wireframe.scnassets/Numbers.scn</key>
		<data>
		0VnOYMxxDfPcDVqjtdVp6jZ+0Vc=
		</data>
		<key>wireframe.scnassets/Particles.scn</key>
		<data>
		Cc2hQ+T32TiL4PiDCYo5wTm7d5Q=
		</data>
		<key>zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9LIKlkeOyWKJqQFP4AIjSKjLKwQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Shi/GjyOXaU+CsI6mdntDrdGAW0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hant.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/7Nwpqze+YSTj5+sUU+3HX8/6+k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+ciEm+qDGp+UZoYEmWF/ETcdQhA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>AppConfig.json</key>
		<dict>
			<key>hash</key>
			<data>
			2fxwgWzWQ11pw411ORNTjc1pwes=
			</data>
			<key>hash2</key>
			<data>
			DZMqEhidVVBeqzB7rHBX+L3F/L7DCROmdXHigK7q/m8=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			mD0mejE6oT3G5NajL8lph4kaom4=
			</data>
			<key>hash2</key>
			<data>
			cyPlDy8uORv4kfba7La655ZTRcxhNSuYaqcXtrQADHY=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			5Qkv3D/08KpgAfJUHNhPDVh3tkk=
			</data>
			<key>hash2</key>
			<data>
			P0BKuMS8IM1Z3K0/JHrSrFKiN26g7bmGGu8wppJ0OBU=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			2aB4nhIVSsS+UwpiGTNWxq8Btmc=
			</data>
			<key>hash2</key>
			<data>
			o8GgzsR+bYcuMOEFxkc+O0xNwsmIszMJO6KzUkLhNqA=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<dict>
			<key>hash</key>
			<data>
			xsX3gt/kMlLTDyjiJhLRD4DdLuQ=
			</data>
			<key>hash2</key>
			<data>
			FE1IOkxx/LbBzo7VFcon0lKPKDuWVFIwRWyAGhKUTI4=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			MDrKFvFWroTb0+KEbQShBcoBvo4=
			</data>
			<key>hash2</key>
			<data>
			PpvapAjR62rl6Ym4E6hkTgpKmBICxTaQXeUqcpHmmqQ=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<dict>
			<key>hash</key>
			<data>
			6YQ54LP2uWyXP6slcH/HQRTrr2M=
			</data>
			<key>hash2</key>
			<data>
			EO0gGlQDXAtztEwWvtMJvXsT1lhDs5UxDi23vMl8LeY=
			</data>
		</dict>
		<key>DemoData.json</key>
		<dict>
			<key>hash</key>
			<data>
			O+FsaJ0BZDWsYv3oV0gwpLSIx1M=
			</data>
			<key>hash2</key>
			<data>
			9ZSwDGjCTuf2WsaRwSV+5hcXb/LqVje/+hyjFYLZvL8=
			</data>
		</dict>
		<key>FirstLaunchIntro.mp4</key>
		<dict>
			<key>hash</key>
			<data>
			w+pOOQcsW6co58XKrPcUZ1lqNMw=
			</data>
			<key>hash2</key>
			<data>
			db0AIVr7UM8Be+QaVnowujYT68nO+Ngyja1AiM0292E=
			</data>
		</dict>
		<key>FoundersGrotesk-Bold.otf</key>
		<dict>
			<key>hash</key>
			<data>
			2onx4TVf2oQUN/N+kWXCX1bMBVM=
			</data>
			<key>hash2</key>
			<data>
			8whaOo1h3QD+5t1hjpoFPWQ8c2QJeBlVgzhiNpQ3/uQ=
			</data>
		</dict>
		<key>FoundersGrotesk-BoldItalic.otf</key>
		<dict>
			<key>hash</key>
			<data>
			CX5j2Du1XPDvIS+rxvRbLIFobr0=
			</data>
			<key>hash2</key>
			<data>
			N3B/INt6V83HoShY7xxhcOPfHgD5ofUXbR7niB6bKFY=
			</data>
		</dict>
		<key>FoundersGrotesk-Regular.otf</key>
		<dict>
			<key>hash</key>
			<data>
			3BnSp4IubDZkP8RBbw1SrpzBg6o=
			</data>
			<key>hash2</key>
			<data>
			HbybZigZsQvvEBa1t+U6ZlbYX2zqKafvO4dB0x9n59I=
			</data>
		</dict>
		<key>FoundersGrotesk-RegularItalic.otf</key>
		<dict>
			<key>hash</key>
			<data>
			QDUI6uUJj6osKw42qy7fb2XZE3g=
			</data>
			<key>hash2</key>
			<data>
			Q/qHHO2IwxAsspM5/tgEdd0bEEKwF27q7W1ZmOUtPN4=
			</data>
		</dict>
		<key>JetBrainsMono-ExtraBold.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			ObWoBva1HqhjvOIzGI2sKR/CGtQ=
			</data>
			<key>hash2</key>
			<data>
			9Yl7j0UuK8G7AtHth3XhZlUmEkDFkGiyC9EZcThFj/g=
			</data>
		</dict>
		<key>JetBrainsMono-Regular.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			1j47G+nzKSg93KFFVPTMf5qjKJw=
			</data>
			<key>hash2</key>
			<data>
			pL1p0JPssbXsC9zBQDcETds//jy5o7zn5Z9zikXDkT8=
			</data>
		</dict>
		<key>Launch Screen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash</key>
			<data>
			JPbtj0W9QAaAmbtV1Xt2XLxrgOY=
			</data>
			<key>hash2</key>
			<data>
			iNjqmFd0NDLZAa5GiEjhddiHev/NIvAxSjPyTWvvvFk=
			</data>
		</dict>
		<key>Launch Screen.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			n2t8gsDpfE6XkhG31p7IQJRxTxU=
			</data>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Launch Screen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash</key>
			<data>
			ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
			</data>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>NeueMaticCompressed-Bold.otf</key>
		<dict>
			<key>hash</key>
			<data>
			OpjwhMvzSHZwHzQZ4KXPGPPR8Z0=
			</data>
			<key>hash2</key>
			<data>
			tNtjyH9JDTfagKk8gVFBbvLtfZSVyRHPM2RW0c5Dmqw=
			</data>
		</dict>
		<key>NeumaticGothic-Bold.otf</key>
		<dict>
			<key>hash</key>
			<data>
			clj2KxA28If2JnI5WvBwna+al4w=
			</data>
			<key>hash2</key>
			<data>
			eTVBlMgVMF4ERUzGM6tDxbEwvjV/w6gB4uRuFI58cIc=
			</data>
		</dict>
		<key>NeumaticGothic-Regular.otf</key>
		<dict>
			<key>hash</key>
			<data>
			TxOmeQAZGG1iqBunXY2xcE9Pr/4=
			</data>
			<key>hash2</key>
			<data>
			JAHB8W+R8+ML7/UKQBEeP0NJb+yAwEPR6hjrm5zWyM8=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			2KnS9FqtJMxN/zDdXi5HWwC4s9k=
			</data>
			<key>hash2</key>
			<data>
			46xoAsj/iv7Ba+6RMnuIxyatbhyYH4geJuLcUaxTLiU=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/FoundersGrotesk-Bold.otf</key>
		<dict>
			<key>hash</key>
			<data>
			2onx4TVf2oQUN/N+kWXCX1bMBVM=
			</data>
			<key>hash2</key>
			<data>
			8whaOo1h3QD+5t1hjpoFPWQ8c2QJeBlVgzhiNpQ3/uQ=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/FoundersGrotesk-BoldItalic.otf</key>
		<dict>
			<key>hash</key>
			<data>
			CX5j2Du1XPDvIS+rxvRbLIFobr0=
			</data>
			<key>hash2</key>
			<data>
			N3B/INt6V83HoShY7xxhcOPfHgD5ofUXbR7niB6bKFY=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/FoundersGrotesk-Regular.otf</key>
		<dict>
			<key>hash</key>
			<data>
			3BnSp4IubDZkP8RBbw1SrpzBg6o=
			</data>
			<key>hash2</key>
			<data>
			HbybZigZsQvvEBa1t+U6ZlbYX2zqKafvO4dB0x9n59I=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/FoundersGrotesk-RegularItalic.otf</key>
		<dict>
			<key>hash</key>
			<data>
			QDUI6uUJj6osKw42qy7fb2XZE3g=
			</data>
			<key>hash2</key>
			<data>
			Q/qHHO2IwxAsspM5/tgEdd0bEEKwF27q7W1ZmOUtPN4=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			nDvSjMXwivEwMX/EUMsSruR2iHc=
			</data>
			<key>hash2</key>
			<data>
			lKb3mxGHZHCh/BTw7pEDNkyBlYPu0/XUcErz/1SOaSY=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/JetBrainsMono-Regular.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			1j47G+nzKSg93KFFVPTMf5qjKJw=
			</data>
			<key>hash2</key>
			<data>
			pL1p0JPssbXsC9zBQDcETds//jy5o7zn5Z9zikXDkT8=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/NeueMaticCompressed-Bold.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			2k9mO0xPIw+HDws6/dnu1Bhzfug=
			</data>
			<key>hash2</key>
			<data>
			tKSBMrwQmMa59DPRcv2Q4Fel/RwQJiRFuyRip7+l/RQ=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/NeumaticGothic-Bold.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			EYsj/rWvyLxnVzQKDxB09HVSmKE=
			</data>
			<key>hash2</key>
			<data>
			I7WJ9ZyM2nj+t4vRoC39+rh/hAv/gOCr7srj2UP0bnQ=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/NeumaticGothic-Regular.ttf</key>
		<dict>
			<key>hash</key>
			<data>
			Zqsr2ZsfnhaBOC7iTrsFx5xImvI=
			</data>
			<key>hash2</key>
			<data>
			YoyEqh0BgbyFNwNYfIFaD3PFiWVRTFsSwFzlnODwG+A=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCat.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			MVZ+U0wtJQF7b0Wc1vq1FzkY86I=
			</data>
			<key>hash2</key>
			<data>
			xQsZsiJSGt/IsaDYQFLvLHr96Tv14ZYkUXX62B2dlIE=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCat.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			uqRDkW3s33psRj8H+jal4YSzsvk=
			</data>
			<key>hash2</key>
			<data>
			dsh2xzvzfWOylE0EkeTZpD1CDeeLZlEvzKJ6SErpLnk=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			VkivHEN0Sq7H7WpXWg2/23RU0nY=
			</data>
			<key>hash2</key>
			<data>
			Tz5m+nyD7kNfykbGT2vBINUZrSTefdkeDK0zn3r28Sk=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			1t40HiElje0wTOPeNb3VuynXqd8=
			</data>
			<key>hash2</key>
			<data>
			zxmWYFxM1nEJ+FztgzwpU3W2as5syAUkahW37jJ1pDM=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IgrDYE7s5Ao1kgXGWhkmE6DtaSw=
			</data>
			<key>hash2</key>
			<data>
			43XLVDTng+RxO2Nl9fMmH+c/h9SHao9PktitA/Zzv0I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/background.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			cobmQyDEiwBkOm53urv6Z0Jheeg=
			</data>
			<key>hash2</key>
			<data>
			dUOKLLcnrAPN0z7XnuVdMdCw1n1XpS67wiyRMR9CCtE=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/bg.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1DK4kp//KoPu6EliBCoHJSm3hSg=
			</data>
			<key>hash2</key>
			<data>
			UcYGOuNA78F50VFNATTgdND4sFlqtw1fA3jB4C0giU8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+0WdXT9iA7rxmekkeFV1CFfUr0M=
			</data>
			<key>hash2</key>
			<data>
			b5oSqB1JdHECbRfm3S/1tZLC0Lh1+KdHY7vGtyYMqQ4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzfk/vzfrNFlQAV78mNv+sM+cLs=
			</data>
			<key>hash2</key>
			<data>
			gg0Ziev9GuMAHbm+irLQSQxKT9nUMKs97An7FiRKUK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/da.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Tb1M+JlXwha6CASNZN99jg1lCjY=
			</data>
			<key>hash2</key>
			<data>
			OVGMstuHYWej4jOlPLROEtWw8XqPVvrvgmcrsifmod4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+1wNXbdjC5n5rXHD3ujvnjU2aOw=
			</data>
			<key>hash2</key>
			<data>
			oODtsRpPj+kqJY/iEx87zD+zNZSWC8IED5HsdfQ7Grk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/el.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			j7zTilv4aawn1JvdeWDti6lfvpY=
			</data>
			<key>hash2</key>
			<data>
			0HHzcsA0JHJCagOM9prG8NTF2yRKYl+6RSHHONYyqkU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/en_AU.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/en_CA.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/en_GB.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/en_US.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/es_419.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rp8pvRolRrgVJOqfZRqqPqT/zB0=
			</data>
			<key>hash2</key>
			<data>
			pDdqBQdRB/UK2nCPJdYqYziGEIvo4Cba0u/5rfkiGC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/es_ES.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rp8pvRolRrgVJOqfZRqqPqT/zB0=
			</data>
			<key>hash2</key>
			<data>
			pDdqBQdRB/UK2nCPJdYqYziGEIvo4Cba0u/5rfkiGC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zPSCYzm5sUbOkWRJLBpML1c7bpI=
			</data>
			<key>hash2</key>
			<data>
			CoJxRV6Pa4Nd7I97UYLcv4Jrek4GcFWK55nF7w1219Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/fr_CA.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fwtMweVYC4tNUSn94QdCECWfosc=
			</data>
			<key>hash2</key>
			<data>
			ESlXxVU7u3wwc8kV7U8m5LZ1geYWZKxspFO3Oe3RDqc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/he.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OOwIfBI8gTKKCd6IG0lAxSVfGHI=
			</data>
			<key>hash2</key>
			<data>
			KXx+u6IQWoTr824w+fPwy4RA0BmZ7rLNPswCkgu0cJk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/hi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XoLnoVnrb8rYzqZkbWn4Y4jmyoM=
			</data>
			<key>hash2</key>
			<data>
			9MU3l/uznWBioc45/UaIxB1dylNR9IwQCoGTo9pWNiM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/hr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v69QiECFoEXKsPyKgwTM6/0+988=
			</data>
			<key>hash2</key>
			<data>
			e6VV5rAcviIgcspYOB6adkf5jkMXJJCoPGqjIhuL3Kk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7U3zm8yKWi31CkYV5toBCx2pHfA=
			</data>
			<key>hash2</key>
			<data>
			xshQSdY9UAEi26wunbyNoa/T4QDen+KhIBk2C21ILp0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/id.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dTay5oBh44GUzpUfM/MYxpycpZk=
			</data>
			<key>hash2</key>
			<data>
			j5r/tRvArqey/5v9T8qd1zpX13DqqgVxmilibx0Sfjk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oq1WnovzEqv8jMG/r3GzZdYL9bg=
			</data>
			<key>hash2</key>
			<data>
			n+gHBC7biprvjHovhOdEWSzjLrAhlJOJ4CKFVrxTn98=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mhl5cJsbiu8E630s+IBtqg/rGS8=
			</data>
			<key>hash2</key>
			<data>
			gFCMNFYa5kp7SH5wfOkUq2EQM750ytvzgICRVrLVero=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/kk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p+pJpe7nUaOnu2TSmYQ/7On9YrI=
			</data>
			<key>hash2</key>
			<data>
			o3YdmKFaoOR1ws7EFJ8g2ciB1pWAf4zjTvqjRtTWu2k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xMJ1BrfbetANxc5M2m4JLDeGvBY=
			</data>
			<key>hash2</key>
			<data>
			PohLRw2CSHs6uubqf4DFU+Qx1xWAZnE9TV2rqoxyR7o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ms.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GJ013SGsBV29jWSP6lMoiNKrlsE=
			</data>
			<key>hash2</key>
			<data>
			Y8vzlr2gAJfyQLYMUgGXXl1XqcBfum+LNMdVkJcHLQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rj/1cU0FueMctW5Idlc4DffFL4A=
			</data>
			<key>hash2</key>
			<data>
			1pAA+jZTTiofbRsaNq8n5XwtzJCGOzKdqd0tJ3h2NaA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/no.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ohd0nuuHYRbQH5m7Iav1VkCBIhE=
			</data>
			<key>hash2</key>
			<data>
			eFgxLmQmhzdTxLRH4giZ2aO2Mkg0KnIO3ncDn9Vuncg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Y7VJO0AodP4+HUtD99S53Fo1eE0=
			</data>
			<key>hash2</key>
			<data>
			guvT7rsPqhNT4GkxCbQ6lWBtsjFev6qySd8m4jnYDr0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/pt_BR.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ouomTBJZPP2rz7ePbMzBtD4zVCk=
			</data>
			<key>hash2</key>
			<data>
			Z5irvoPF5zXqIqhlnUAVrCeVHTjHqVk4Cfvnard/fQ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/pt_PT.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ouomTBJZPP2rz7ePbMzBtD4zVCk=
			</data>
			<key>hash2</key>
			<data>
			Z5irvoPF5zXqIqhlnUAVrCeVHTjHqVk4Cfvnard/fQ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XdJv/J/wOKxJMc4m1bBz6QiG8xA=
			</data>
			<key>hash2</key>
			<data>
			GtQPLiPA+Uk53qR5drn7D/ncWt2yhBH6ygD3xN25F3M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gwj1mlzWt5aADE1iE+9KDdkAzhw=
			</data>
			<key>hash2</key>
			<data>
			fxWh6lhtonsBacOb7VKKPmXQ6fqla3piCYLscgCVZ5I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TfCNaaj825wCwvrXLJ8bozt6Mxo=
			</data>
			<key>hash2</key>
			<data>
			dVMrrEuRiOMMS1eBLCRxai+LPDaQ7OrYV5zh2zRNBGw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5stHn6nvQv0kKmzer+shwoxHgVw=
			</data>
			<key>hash2</key>
			<data>
			PNcifbuPjOUyFmdDi0+cVhbIKpvGlntSODoqr4YXoSM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/th.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8fWP6ZGNpnRL2mozleLAHZu+asE=
			</data>
			<key>hash2</key>
			<data>
			NpsWECdHUeJrOZfEspsXf4s24BbRRJGYcUQfkrlVHkY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AyHk7CPwbQ/0lK4wnv+2YTBsYmE=
			</data>
			<key>hash2</key>
			<data>
			9jPnfcLSTyByMPQkGNLOlcLAVRV3cmeaRWnt9vKIduU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/uk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uXBx8YT+kSTOMpZhEST69s90wIc=
			</data>
			<key>hash2</key>
			<data>
			da2IURR8kJyt0L3GcWe4zc2p/n8cYHeLq1JD4H17los=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/vi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9b8184pr3yNjMqWMKU8oyGTuGjE=
			</data>
			<key>hash2</key>
			<data>
			iaiXbEUOQKHQpYx2LFR/Uo17Q7HtZi1SuJZIJRrLADQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/zh_Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XfpxFjPoJ1mnulmHM595TUxkDfs=
			</data>
			<key>hash2</key>
			<data>
			B/Cd47WjB1eLekpDRWtOEg8B5XlGB7i8DwzX5xDiSMg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/RevenueCat_RevenueCatUI.bundle/zh_Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0DPdeshPj2dIZd5BIzKTZhyQmgg=
			</data>
			<key>hash2</key>
			<data>
			DRu8+ryqih5ps2AiFXXdhc+WzTE1PO9rRy54GZof3Qo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			QuY1SRVaDhGUKRnTDG7pGEGtTGk=
			</data>
			<key>hash2</key>
			<data>
			fGnb9cDagxtyZQf+EZMh/pMwvHmd4VG7tVcvRdW+sig=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			icomyb6ys7NNbVOtazV8NyG9qCo=
			</data>
			<key>hash2</key>
			<data>
			xGARvdtdNHaznX/CaaxpV5jGbsqDj03cHWFPQCW2tmE=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zzQ2NXhAgc2EwqNSrghGyx75Zsk=
			</data>
			<key>hash2</key>
			<data>
			XklvUMEVmikU0nWUS3YR394W5o5s8BCULNiGug7OHWk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CbHT+zxH4kLofQyjzZqOn9U/XuU=
			</data>
			<key>hash2</key>
			<data>
			2NE7okTcdorba2w2FAt2yUbXHRavMhBMgaXP8jzOl6o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PEPEDUKRjYVTc9rZmKejmxuAu3M=
			</data>
			<key>hash2</key>
			<data>
			ZMdyVfEtjdSiwAEeVJV1e0g1cw1POl4bN4qG0vbnIjs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/explode-open.ahap</key>
		<dict>
			<key>hash</key>
			<data>
			VAAR35KfKPUX5E+tFwedxZlNJUo=
			</data>
			<key>hash2</key>
			<data>
			SwGgC+ZQcEOmSzh8XWviqiQWstN8FCM4UzIi6SYtdgw=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qwFbqEe7kyM+4s62SMsPOhNdEUo=
			</data>
			<key>hash2</key>
			<data>
			aSfo6FOoUzQq2lleHzFFaMJz+QDKCZG1fLwhjsfiOeU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qSuDCVKNGwFjGLeFtsK9eMCV4a8=
			</data>
			<key>hash2</key>
			<data>
			CoNfGY5mn1c7EpoJWtK/HVu/LU+rL/JYk86ipRbsE5A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7HVCabV9EkQSS02uDL+rXCxyXOY=
			</data>
			<key>hash2</key>
			<data>
			a3+lgEtoRLsvIerzNwiFRFQJOduCQX2BLAtzSqCNgvI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i91b5xMeECm4CpmeJQpIHYXuM9Q=
			</data>
			<key>hash2</key>
			<data>
			IXWO+IqyhuAIuFlDIkh7dujYTkQq3mkiAmliYVWDyBc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PWYvfp57Fjn115z1QgQ2u3WXXd0=
			</data>
			<key>hash2</key>
			<data>
			FEMrk3dQj6ETqtPcUYdje3/2YMP1+vkcma9AzhhwMmA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/SimpleCore_SimpleCore.bundle/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Kz6CRitjor6eDbhbEEqNpb5J7rE=
			</data>
			<key>hash2</key>
			<data>
			AvWsO6kIkfRUeULN/NbFW2B55AWvN9TOzLKRzkHyPS4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			7sfD+eWWzIC/V9yyT69JL67U4uE=
			</data>
			<key>hash2</key>
			<data>
			8LVUOTs61N0URsAQAAk80bVQU/dH2RLBifJ8L/mepk0=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/andy-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			4jcmNT2NgfK5sDTc6WBk14nGqiU=
			</data>
			<key>hash2</key>
			<data>
			bfeTDRYdYnmpm4LVZBW3T/STJRMuT4WWLOyWDKGMj98=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/andy-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			1q9gV9plEJkY1/VBOwPlOEpsbzQ=
			</data>
			<key>hash2</key>
			<data>
			xnrLLoUyTV2Fe+h5Yl8d0xy0uYF6xdXzfbLDTag9qak=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/andy-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			Y+w8FBhrkhcbFJtP937+cb3eGyQ=
			</data>
			<key>hash2</key>
			<data>
			DL9PbBsFnpzfYjdjaOuDbWZu6m4CxPmTjAQPxgNAikw=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/base1-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			JkEC572cv5WTKSHKxpNuo82gNYI=
			</data>
			<key>hash2</key>
			<data>
			llP2PBXsqb/qhKZO+29WAj8/Nc2W4fg7AcvN4lpIV7Y=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/bricks-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			P1+SaemHjcyujmgUm1t/FwGxYWk=
			</data>
			<key>hash2</key>
			<data>
			ea48oycLwxivoNVnBYJDO77RZOg6fIsm5pCHkCB77Xo=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/bricks-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			nyzxG2TyHd5NtldIqNnUQM/aon8=
			</data>
			<key>hash2</key>
			<data>
			bCYpgLO+nN/NkKOIermt+okPuix5IISaL04OTb6Vh/M=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/bricks-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			VzPY0XwcukSTzgAF+CiU+gZsnxw=
			</data>
			<key>hash2</key>
			<data>
			4CXgh4wKH2hjkWh8E7lDzvUZKlak7cvih1rTegx+e68=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/cedar-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			Jd2PtxGD1MmcLk0cGgW9I9RpN4w=
			</data>
			<key>hash2</key>
			<data>
			8JuqXkNtL06AMBNi+chpJV3PlO3h0v+1OZR6tnSWpSM=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/cedar-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			nAOcGGMgqJqL5iNos2AutWeV3nc=
			</data>
			<key>hash2</key>
			<data>
			FNzaZMMd6KXQcXPLCXJD5d58sDA4/uFM+fdNRsckPxg=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/cedar-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			aHOIurzBqkzl3ksBfe8gS5fi1d8=
			</data>
			<key>hash2</key>
			<data>
			W+PxGURLULtPfo2IUTW3OItA/K/jp0dIDs6iv2BieYc=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/chroma-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			RUupW9F8QlvVdsAGvZm4Q+D20Ko=
			</data>
			<key>hash2</key>
			<data>
			BV/Q2srFePdBEs5Jl91qt+8BaWwGG5Q1bqX4i4uCtrc=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/chroma-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			dtx1Kc3YLWxPaHUsznENRU9tLcQ=
			</data>
			<key>hash2</key>
			<data>
			mOjA4jAC+EA680YnkPxwjyD1dF1ChrnNbWl9u2BR1oI=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/chroma-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			stGwudiXtOLqJl1VTczFzTGE+ZU=
			</data>
			<key>hash2</key>
			<data>
			3HxFhNDcswPFF23Ys4d0R5okU1FP9OaxwIiyCwETrac=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LTDON6YEF7ST70ahhzpRlX+dllo=
			</data>
			<key>hash2</key>
			<data>
			pATwe6R/mTyfKctJaAAwaMfm3K4DaD96V6geODZVMeo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/depth-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			i6AHSjdO9nTgEpGKiYpKnCwd47E=
			</data>
			<key>hash2</key>
			<data>
			b3UnZq8mbt+dSiQzQVugRFiau7z8bn/T/nIN6pnGCcY=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/depth-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			PqAdyFaK6bIPty7YiB1cXxv7owc=
			</data>
			<key>hash2</key>
			<data>
			coaBrUG2i1+9Y/1MSHiQxJPtNktsaqZXcsd+3oOUo+Y=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/depth-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			yZ1JyHEvjpSoiYQc9jN7FD+49CA=
			</data>
			<key>hash2</key>
			<data>
			UszUnMNaUUEjX2SRUeXYYNVQkuayEphJNsZgmw30Pj0=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p+nwJG17/r4UQWIvuyAQPZDS7uM=
			</data>
			<key>hash2</key>
			<data>
			OZ+EgXhddKr/9sGyvGIHTl/V+BCybQSqcXteZvuD4+c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/EHBdA5VdycM64mhhD4lJJsUVP4=
			</data>
			<key>hash2</key>
			<data>
			9aFIjTuVS8FbF2Z8ugiePol2qgBZ5UcGhyKDfXAoDfM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mSmC+D9j02gAPc1cwywbssQiBKQ=
			</data>
			<key>hash2</key>
			<data>
			tQWvVjd1KS9WQkf6ZZi78V4cdDFoKqdPfcwIGC2h9Do=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cVBkX8o0wGEzHC1mqOzEvPkmlLU=
			</data>
			<key>hash2</key>
			<data>
			upAO4eVipBmjyPr6EYVjhrBVMfDmsBNdtmZcHdUrERE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/graphite-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			YR7wQ7O/RhHvqBigzewKV/syQZQ=
			</data>
			<key>hash2</key>
			<data>
			fCox5ozYoYyHRaHgZMtoFRuz+bM+/nvINF8B+B8qXjo=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/graphite-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			np7EJs3SHyGXbeA+fX/cfEzy480=
			</data>
			<key>hash2</key>
			<data>
			rgbkUPziEcNyZRpVRXOIlOYwWbtOXtYkhzEJK7H0OeU=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/graphite-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			LqeVMAoHG+oCR5NyV/+9Hz4QJBM=
			</data>
			<key>hash2</key>
			<data>
			LTdtVyCi/S7n7PUBMXqihP6MQxFAUb0gYdH5z5EQk4E=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2P+PjoufHmOEDNu336ve6rqLDAM=
			</data>
			<key>hash2</key>
			<data>
			L0NtRL12UKi/IFxM5VW5yZgXCd1cCx3UK0Dsfu954Og=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nELECNjh/DXfP4ElOEfZe7X4XIA=
			</data>
			<key>hash2</key>
			<data>
			zt8A4MrLCXRdOSdYL9JhCI65eY+HVMgXhhwuvUfCdmc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/karat-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			gAGkywTUQ2hFODLtmtC7gEwXP3c=
			</data>
			<key>hash2</key>
			<data>
			2Eba/ijovSgWZZGF7v+WsY3+qDfppJeTpcVE2lx/CqY=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/karat-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			+Mq70YusyQAn+9A4Y1D2F4AMigs=
			</data>
			<key>hash2</key>
			<data>
			wJX5kmWVgr5QeOsBS6LaBJE3kKqJ2lOpa/bxix5ToWw=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/karat-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			ys3gO/H606017EQix6ZrYbu97yA=
			</data>
			<key>hash2</key>
			<data>
			KxybT7xdC9+uRg1eGiPSiPUAXzBf8YFXeq8UyF3ZgFo=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/wuFqBA3/wO0M0tKH1oW+Qlir9Q=
			</data>
			<key>hash2</key>
			<data>
			LTi1Q8RTDCQ8VLxoCadK5RZAr6ZrXz5a+v+DfjLQLds=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/monster-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			aEtpqqeq25BOAsESeZjUYMwK1YI=
			</data>
			<key>hash2</key>
			<data>
			QbpXpi9DTr1pFU5MvibHHo3z7GoLVtjXfUB3hlfJdho=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/monster-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			NItWhVRF0e5NKx61tLhQsS+JVgs=
			</data>
			<key>hash2</key>
			<data>
			dLFA1KF2NvvxH3bU6Akqp+psUyMVL7Fef1Qc3KpK1sk=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/monster-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			OTuCV/89NlfbPugIWset1pcmTAk=
			</data>
			<key>hash2</key>
			<data>
			z/j2djFJ78/Z44NBRiIeTajU1RNgUQtT5L8+AFiwPGA=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/normal-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			cf3RwXmcWshBcK2KLXyob6vO1j0=
			</data>
			<key>hash2</key>
			<data>
			pA7/nksYB+eZDDheRAgP1V5cptq9cqjvp6sGeFBoME4=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/normal-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			8YiWkSvW0zx9OVrCiilifY6iCCQ=
			</data>
			<key>hash2</key>
			<data>
			gI0frTxtP3iGd/e0g6mZntjcpBECMexDv/qeZw1a4+4=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/normal-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			E7YQDPKEN6UPLK8vqrW7ynr5N6U=
			</data>
			<key>hash2</key>
			<data>
			Mzj/NgiKLhZPBEdKfhNGlUerlAQsisuwoLchKTqO+RU=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/opal-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			JxA+QKOggC+LfCpsyltQYX8TGX8=
			</data>
			<key>hash2</key>
			<data>
			ozcv7rePcJwJE3xSEIzfY9WeuXcO+us0ezzZzZ4RAVM=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/opal-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			lRY8D0TWIc8/rI6FvomnxAdGv/w=
			</data>
			<key>hash2</key>
			<data>
			BjESPbFkNIrLSX/nSRYhCFy2HK6CW6gEti0/IdcQKeI=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/opal-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			W8U9oQe4l5m28/gHjL8ZjtVvpuc=
			</data>
			<key>hash2</key>
			<data>
			GSLwZA4IZQD8s2R3zUDJhr0fKp/zJNmwBzKHSGlklPQ=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/presstube-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			Jh/2brca/CFAkquNIrJuhQdDC9g=
			</data>
			<key>hash2</key>
			<data>
			dBF50qjDxBrBPguAOunECVipbRsoiOg3syhs5ql/lo8=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/presstube-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			hRGtshy6vCyOXnkqMXig+f4hvY8=
			</data>
			<key>hash2</key>
			<data>
			CHZfpy+4QzL3sxDr9S4a6e2lOqbaVjRMQDTFBApOa7w=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/presstube-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			P8bZldX26ircgfJIKVyDMzuYeK8=
			</data>
			<key>hash2</key>
			<data>
			yL6tFK1dIJsgm502PRAkP10FN4xGPIbmqbOeJlWhRWI=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/widget-mainExtension</key>
		<dict>
			<key>hash</key>
			<data>
			ncM415ic6Pex5JUrWl+P81XZ6TQ=
			</data>
			<key>hash2</key>
			<data>
			6R5mKNTTBwLgwX/554xvPlnKs53yx7N0YtOpHzUjHBY=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/wireframe-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			oPf7NGvKfp9A/y1DmScXTxiRbzs=
			</data>
			<key>hash2</key>
			<data>
			NJzVFC6uZG5N9/qy+4UzDWt1zzvZiqw1ecPw0Fgh9vs=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/wireframe-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			SkET4REpucuQlvEopWhZgzy3xvY=
			</data>
			<key>hash2</key>
			<data>
			7DX6SYJJ8oNb30ouU3mQFIaVOXHpJr2EDpTKKerWq48=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/wireframe-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			8ji1OdI/yl3KdcG520CtnE7BUCE=
			</data>
			<key>hash2</key>
			<data>
			jZsIJ5Tbo5Mjwkqj+G2GWH5sLwTbFt90hIEDrk5l1O0=
			</data>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HGf0fK/p7N26ga/Y9qcO9p63+AA=
			</data>
			<key>hash2</key>
			<data>
			BSklcEBMndWgLqsUu08FMTk2H/usXtaOBMQS3EfSn18=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Shi/GjyOXaU+CsI6mdntDrdGAW0=
			</data>
			<key>hash2</key>
			<data>
			WYIHFBaN+pGrE3tpPWaqlGI4FPDPF0IZigbiVXbRHrM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/widget-mainExtension.appex/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+ciEm+qDGp+UZoYEmWF/ETcdQhA=
			</data>
			<key>hash2</key>
			<data>
			XjAQ/6tUd/9Oo4HuqlW1gDE2aVZnc+4gveFjkxo3G24=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			twc8slLn0q5hstPg5A8z0a+5Rq4=
			</data>
			<key>hash2</key>
			<data>
			g21OggbLl0+LL1RJzArFaXGYf9LO0+vlQ6wzukZrjGY=
			</data>
		</dict>
		<key>RevenueCat_RevenueCat.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			MVZ+U0wtJQF7b0Wc1vq1FzkY86I=
			</data>
			<key>hash2</key>
			<data>
			xQsZsiJSGt/IsaDYQFLvLHr96Tv14ZYkUXX62B2dlIE=
			</data>
		</dict>
		<key>RevenueCat_RevenueCat.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			uqRDkW3s33psRj8H+jal4YSzsvk=
			</data>
			<key>hash2</key>
			<data>
			dsh2xzvzfWOylE0EkeTZpD1CDeeLZlEvzKJ6SErpLnk=
			</data>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			VkivHEN0Sq7H7WpXWg2/23RU0nY=
			</data>
			<key>hash2</key>
			<data>
			Tz5m+nyD7kNfykbGT2vBINUZrSTefdkeDK0zn3r28Sk=
			</data>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			1t40HiElje0wTOPeNb3VuynXqd8=
			</data>
			<key>hash2</key>
			<data>
			zxmWYFxM1nEJ+FztgzwpU3W2as5syAUkahW37jJ1pDM=
			</data>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ar.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IgrDYE7s5Ao1kgXGWhkmE6DtaSw=
			</data>
			<key>hash2</key>
			<data>
			43XLVDTng+RxO2Nl9fMmH+c/h9SHao9PktitA/Zzv0I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/background.jpg</key>
		<dict>
			<key>hash</key>
			<data>
			cobmQyDEiwBkOm53urv6Z0Jheeg=
			</data>
			<key>hash2</key>
			<data>
			dUOKLLcnrAPN0z7XnuVdMdCw1n1XpS67wiyRMR9CCtE=
			</data>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/bg.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1DK4kp//KoPu6EliBCoHJSm3hSg=
			</data>
			<key>hash2</key>
			<data>
			UcYGOuNA78F50VFNATTgdND4sFlqtw1fA3jB4C0giU8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ca.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+0WdXT9iA7rxmekkeFV1CFfUr0M=
			</data>
			<key>hash2</key>
			<data>
			b5oSqB1JdHECbRfm3S/1tZLC0Lh1+KdHY7vGtyYMqQ4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/cs.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Dzfk/vzfrNFlQAV78mNv+sM+cLs=
			</data>
			<key>hash2</key>
			<data>
			gg0Ziev9GuMAHbm+irLQSQxKT9nUMKs97An7FiRKUK0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/da.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Tb1M+JlXwha6CASNZN99jg1lCjY=
			</data>
			<key>hash2</key>
			<data>
			OVGMstuHYWej4jOlPLROEtWw8XqPVvrvgmcrsifmod4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+1wNXbdjC5n5rXHD3ujvnjU2aOw=
			</data>
			<key>hash2</key>
			<data>
			oODtsRpPj+kqJY/iEx87zD+zNZSWC8IED5HsdfQ7Grk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/el.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			j7zTilv4aawn1JvdeWDti6lfvpY=
			</data>
			<key>hash2</key>
			<data>
			0HHzcsA0JHJCagOM9prG8NTF2yRKYl+6RSHHONYyqkU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_AU.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_CA.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_GB.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/en_US.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6jwOAHnNOKHDCVDX+ZvwNSedzwk=
			</data>
			<key>hash2</key>
			<data>
			gY+7Yt/FdWKA7oDdduguAtg1mZ+3b9veqko+wr09mC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/es_419.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rp8pvRolRrgVJOqfZRqqPqT/zB0=
			</data>
			<key>hash2</key>
			<data>
			pDdqBQdRB/UK2nCPJdYqYziGEIvo4Cba0u/5rfkiGC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/es_ES.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rp8pvRolRrgVJOqfZRqqPqT/zB0=
			</data>
			<key>hash2</key>
			<data>
			pDdqBQdRB/UK2nCPJdYqYziGEIvo4Cba0u/5rfkiGC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/fi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zPSCYzm5sUbOkWRJLBpML1c7bpI=
			</data>
			<key>hash2</key>
			<data>
			CoJxRV6Pa4Nd7I97UYLcv4Jrek4GcFWK55nF7w1219Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/fr_CA.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fwtMweVYC4tNUSn94QdCECWfosc=
			</data>
			<key>hash2</key>
			<data>
			ESlXxVU7u3wwc8kV7U8m5LZ1geYWZKxspFO3Oe3RDqc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/he.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			OOwIfBI8gTKKCd6IG0lAxSVfGHI=
			</data>
			<key>hash2</key>
			<data>
			KXx+u6IQWoTr824w+fPwy4RA0BmZ7rLNPswCkgu0cJk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/hi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XoLnoVnrb8rYzqZkbWn4Y4jmyoM=
			</data>
			<key>hash2</key>
			<data>
			9MU3l/uznWBioc45/UaIxB1dylNR9IwQCoGTo9pWNiM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/hr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v69QiECFoEXKsPyKgwTM6/0+988=
			</data>
			<key>hash2</key>
			<data>
			e6VV5rAcviIgcspYOB6adkf5jkMXJJCoPGqjIhuL3Kk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/hu.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7U3zm8yKWi31CkYV5toBCx2pHfA=
			</data>
			<key>hash2</key>
			<data>
			xshQSdY9UAEi26wunbyNoa/T4QDen+KhIBk2C21ILp0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/id.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dTay5oBh44GUzpUfM/MYxpycpZk=
			</data>
			<key>hash2</key>
			<data>
			j5r/tRvArqey/5v9T8qd1zpX13DqqgVxmilibx0Sfjk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			oq1WnovzEqv8jMG/r3GzZdYL9bg=
			</data>
			<key>hash2</key>
			<data>
			n+gHBC7biprvjHovhOdEWSzjLrAhlJOJ4CKFVrxTn98=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mhl5cJsbiu8E630s+IBtqg/rGS8=
			</data>
			<key>hash2</key>
			<data>
			gFCMNFYa5kp7SH5wfOkUq2EQM750ytvzgICRVrLVero=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/kk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p+pJpe7nUaOnu2TSmYQ/7On9YrI=
			</data>
			<key>hash2</key>
			<data>
			o3YdmKFaoOR1ws7EFJ8g2ciB1pWAf4zjTvqjRtTWu2k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xMJ1BrfbetANxc5M2m4JLDeGvBY=
			</data>
			<key>hash2</key>
			<data>
			PohLRw2CSHs6uubqf4DFU+Qx1xWAZnE9TV2rqoxyR7o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ms.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GJ013SGsBV29jWSP6lMoiNKrlsE=
			</data>
			<key>hash2</key>
			<data>
			Y8vzlr2gAJfyQLYMUgGXXl1XqcBfum+LNMdVkJcHLQI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/nl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rj/1cU0FueMctW5Idlc4DffFL4A=
			</data>
			<key>hash2</key>
			<data>
			1pAA+jZTTiofbRsaNq8n5XwtzJCGOzKdqd0tJ3h2NaA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/no.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ohd0nuuHYRbQH5m7Iav1VkCBIhE=
			</data>
			<key>hash2</key>
			<data>
			eFgxLmQmhzdTxLRH4giZ2aO2Mkg0KnIO3ncDn9Vuncg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/pl.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Y7VJO0AodP4+HUtD99S53Fo1eE0=
			</data>
			<key>hash2</key>
			<data>
			guvT7rsPqhNT4GkxCbQ6lWBtsjFev6qySd8m4jnYDr0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/pt_BR.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ouomTBJZPP2rz7ePbMzBtD4zVCk=
			</data>
			<key>hash2</key>
			<data>
			Z5irvoPF5zXqIqhlnUAVrCeVHTjHqVk4Cfvnard/fQ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/pt_PT.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ouomTBJZPP2rz7ePbMzBtD4zVCk=
			</data>
			<key>hash2</key>
			<data>
			Z5irvoPF5zXqIqhlnUAVrCeVHTjHqVk4Cfvnard/fQ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ro.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XdJv/J/wOKxJMc4m1bBz6QiG8xA=
			</data>
			<key>hash2</key>
			<data>
			GtQPLiPA+Uk53qR5drn7D/ncWt2yhBH6ygD3xN25F3M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/ru.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gwj1mlzWt5aADE1iE+9KDdkAzhw=
			</data>
			<key>hash2</key>
			<data>
			fxWh6lhtonsBacOb7VKKPmXQ6fqla3piCYLscgCVZ5I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/sk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TfCNaaj825wCwvrXLJ8bozt6Mxo=
			</data>
			<key>hash2</key>
			<data>
			dVMrrEuRiOMMS1eBLCRxai+LPDaQ7OrYV5zh2zRNBGw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/sv.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5stHn6nvQv0kKmzer+shwoxHgVw=
			</data>
			<key>hash2</key>
			<data>
			PNcifbuPjOUyFmdDi0+cVhbIKpvGlntSODoqr4YXoSM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/th.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8fWP6ZGNpnRL2mozleLAHZu+asE=
			</data>
			<key>hash2</key>
			<data>
			NpsWECdHUeJrOZfEspsXf4s24BbRRJGYcUQfkrlVHkY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/tr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			AyHk7CPwbQ/0lK4wnv+2YTBsYmE=
			</data>
			<key>hash2</key>
			<data>
			9jPnfcLSTyByMPQkGNLOlcLAVRV3cmeaRWnt9vKIduU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/uk.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uXBx8YT+kSTOMpZhEST69s90wIc=
			</data>
			<key>hash2</key>
			<data>
			da2IURR8kJyt0L3GcWe4zc2p/n8cYHeLq1JD4H17los=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/vi.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9b8184pr3yNjMqWMKU8oyGTuGjE=
			</data>
			<key>hash2</key>
			<data>
			iaiXbEUOQKHQpYx2LFR/Uo17Q7HtZi1SuJZIJRrLADQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/zh_Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XfpxFjPoJ1mnulmHM595TUxkDfs=
			</data>
			<key>hash2</key>
			<data>
			B/Cd47WjB1eLekpDRWtOEg8B5XlGB7i8DwzX5xDiSMg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>RevenueCat_RevenueCatUI.bundle/zh_Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			0DPdeshPj2dIZd5BIzKTZhyQmgg=
			</data>
			<key>hash2</key>
			<data>
			DRu8+ryqih5ps2AiFXXdhc+WzTE1PO9rRy54GZof3Qo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SC_Info/Manifest.plist</key>
		<dict>
			<key>hash</key>
			<data>
			8bluJJg6Z1LeDK/8chpPtnnQBIk=
			</data>
			<key>hash2</key>
			<data>
			QOQNgFZjlSUbGsVtEYrnhqa7vbJJP206Qf1+kFE3XG8=
			</data>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			QuY1SRVaDhGUKRnTDG7pGEGtTGk=
			</data>
			<key>hash2</key>
			<data>
			fGnb9cDagxtyZQf+EZMh/pMwvHmd4VG7tVcvRdW+sig=
			</data>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			icomyb6ys7NNbVOtazV8NyG9qCo=
			</data>
			<key>hash2</key>
			<data>
			xGARvdtdNHaznX/CaaxpV5jGbsqDj03cHWFPQCW2tmE=
			</data>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zzQ2NXhAgc2EwqNSrghGyx75Zsk=
			</data>
			<key>hash2</key>
			<data>
			XklvUMEVmikU0nWUS3YR394W5o5s8BCULNiGug7OHWk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CbHT+zxH4kLofQyjzZqOn9U/XuU=
			</data>
			<key>hash2</key>
			<data>
			2NE7okTcdorba2w2FAt2yUbXHRavMhBMgaXP8jzOl6o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PEPEDUKRjYVTc9rZmKejmxuAu3M=
			</data>
			<key>hash2</key>
			<data>
			ZMdyVfEtjdSiwAEeVJV1e0g1cw1POl4bN4qG0vbnIjs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/explode-open.ahap</key>
		<dict>
			<key>hash</key>
			<data>
			VAAR35KfKPUX5E+tFwedxZlNJUo=
			</data>
			<key>hash2</key>
			<data>
			SwGgC+ZQcEOmSzh8XWviqiQWstN8FCM4UzIi6SYtdgw=
			</data>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qwFbqEe7kyM+4s62SMsPOhNdEUo=
			</data>
			<key>hash2</key>
			<data>
			aSfo6FOoUzQq2lleHzFFaMJz+QDKCZG1fLwhjsfiOeU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qSuDCVKNGwFjGLeFtsK9eMCV4a8=
			</data>
			<key>hash2</key>
			<data>
			CoNfGY5mn1c7EpoJWtK/HVu/LU+rL/JYk86ipRbsE5A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7HVCabV9EkQSS02uDL+rXCxyXOY=
			</data>
			<key>hash2</key>
			<data>
			a3+lgEtoRLsvIerzNwiFRFQJOduCQX2BLAtzSqCNgvI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			i91b5xMeECm4CpmeJQpIHYXuM9Q=
			</data>
			<key>hash2</key>
			<data>
			IXWO+IqyhuAIuFlDIkh7dujYTkQq3mkiAmliYVWDyBc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PWYvfp57Fjn115z1QgQ2u3WXXd0=
			</data>
			<key>hash2</key>
			<data>
			FEMrk3dQj6ETqtPcUYdje3/2YMP1+vkcma9AzhhwMmA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SimpleCore_SimpleCore.bundle/zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Kz6CRitjor6eDbhbEEqNpb5J7rE=
			</data>
			<key>hash2</key>
			<data>
			AvWsO6kIkfRUeULN/NbFW2B55AWvN9TOzLKRzkHyPS4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>andy-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			4jcmNT2NgfK5sDTc6WBk14nGqiU=
			</data>
			<key>hash2</key>
			<data>
			bfeTDRYdYnmpm4LVZBW3T/STJRMuT4WWLOyWDKGMj98=
			</data>
		</dict>
		<key>andy-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			1q9gV9plEJkY1/VBOwPlOEpsbzQ=
			</data>
			<key>hash2</key>
			<data>
			xnrLLoUyTV2Fe+h5Yl8d0xy0uYF6xdXzfbLDTag9qak=
			</data>
		</dict>
		<key>andy-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			Y+w8FBhrkhcbFJtP937+cb3eGyQ=
			</data>
			<key>hash2</key>
			<data>
			DL9PbBsFnpzfYjdjaOuDbWZu6m4CxPmTjAQPxgNAikw=
			</data>
		</dict>
		<key>andy.scnassets/Main.scn</key>
		<dict>
			<key>hash</key>
			<data>
			i1ywSI0I8vCuw/IjOV9i2SVdA4s=
			</data>
			<key>hash2</key>
			<data>
			utCPPEdHPBR93b2qbDDb9NemmqPOIgPbYjBlahtgBwQ=
			</data>
		</dict>
		<key>andy.scnassets/Models.scn</key>
		<dict>
			<key>hash</key>
			<data>
			iifdbn/SOqTGxyGPFKH/HDvuse4=
			</data>
			<key>hash2</key>
			<data>
			xAQel8wZYLYFq/Vb8ZPdsFEv8pOMLDFpkJHlf04qs50=
			</data>
		</dict>
		<key>andy.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			dJ5rM6TDU/NKlhcmviAk2llzDvM=
			</data>
			<key>hash2</key>
			<data>
			aoZvTeA+F5JeU/j5OUfX3nTf+3jHmdiH8bT3zrvt8TI=
			</data>
		</dict>
		<key>andy.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			hyl4fC5E5gLh4v321KxeBJbz+e4=
			</data>
			<key>hash2</key>
			<data>
			Kc2Ku7yz2JPKuAo23LROW6aTqR3AxRdjOGaejIhNaHY=
			</data>
		</dict>
		<key>app-intro.caf</key>
		<dict>
			<key>hash</key>
			<data>
			68yK1sK/HEbBffp4J29jDPlpTwE=
			</data>
			<key>hash2</key>
			<data>
			35u+Ho31cnE6y+tPZ2qY+cxyHgCYF8tFtPOZJNmAJYk=
			</data>
		</dict>
		<key>base1-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			JkEC572cv5WTKSHKxpNuo82gNYI=
			</data>
			<key>hash2</key>
			<data>
			llP2PBXsqb/qhKZO+29WAj8/Nc2W4fg7AcvN4lpIV7Y=
			</data>
		</dict>
		<key>base1.scnassets/Animation-CameraShake.scn</key>
		<dict>
			<key>hash</key>
			<data>
			V5YFhtWfsymZgfqvbWpQ/Ye4yds=
			</data>
			<key>hash2</key>
			<data>
			lOAOFhFCTZrRo40aUfmXnVbJqAIfV8GZcoljyMdM54Q=
			</data>
		</dict>
		<key>base1.scnassets/Animation-bolt.scn</key>
		<dict>
			<key>hash</key>
			<data>
			hcISiAIXHUTfcriIpXKqZ2VEbxk=
			</data>
			<key>hash2</key>
			<data>
			E0a1fUwfgrxVqvNNArxkHShMDYrzsDSioQKXlL7I3rQ=
			</data>
		</dict>
		<key>base1.scnassets/Animation-cloud-in.scn</key>
		<dict>
			<key>hash</key>
			<data>
			m5oCCkKCeEo5vFPRYynxOh5y9DE=
			</data>
			<key>hash2</key>
			<data>
			4wUiBDEUezASd2HPZIb8K9ZZkuTSkpSpwa8Q0PAPe+Q=
			</data>
		</dict>
		<key>base1.scnassets/Animation-cloud-out.scn</key>
		<dict>
			<key>hash</key>
			<data>
			CetYuTvlGZeZ5kYZcXZ9Jqa+Oi8=
			</data>
			<key>hash2</key>
			<data>
			BpnqanWuvXE6oYMu3N9LMOL9On/pCBd/X6s77RkCy5Y=
			</data>
		</dict>
		<key>base1.scnassets/Animation-cloud.scn</key>
		<dict>
			<key>hash</key>
			<data>
			OuDAACkyhoAAtCM/ZkqrlF0OS04=
			</data>
			<key>hash2</key>
			<data>
			TP1zIn0W7OS6DAgf5nNhsKUEI+hQEJ44v21+sbQw0uE=
			</data>
		</dict>
		<key>base1.scnassets/Animation-fog.scn</key>
		<dict>
			<key>hash</key>
			<data>
			HqnRa1RpIhjAiKrXBjq4r7RCyts=
			</data>
			<key>hash2</key>
			<data>
			/P20+cbM1kZFHFFeFFyCuCof/MwaCGMpWyAbgOzhi0U=
			</data>
		</dict>
		<key>base1.scnassets/Animation-particle-pop.scn</key>
		<dict>
			<key>hash</key>
			<data>
			lvLBFu6hVqAv1DCpXTxefW/WJX4=
			</data>
			<key>hash2</key>
			<data>
			AcpJEthgNSGuiQKsUgecpoiVjfaKkXBZxCNavOtjTII=
			</data>
		</dict>
		<key>base1.scnassets/Animation-pop.scn</key>
		<dict>
			<key>hash</key>
			<data>
			2/KZMHn0FP1jhyi6/nxy3DQsL4Y=
			</data>
			<key>hash2</key>
			<data>
			90Pg8PwBWMsc4ajxCyY1rJdIT5U0E94e1Y1s6Gtmy2Y=
			</data>
		</dict>
		<key>base1.scnassets/Animation-sun-in.scn</key>
		<dict>
			<key>hash</key>
			<data>
			OsRDr+nTu5ykDopYl6HeKTq3sVw=
			</data>
			<key>hash2</key>
			<data>
			AnNA07rNaoDf8hohwZ0neYDkpnMtuoRhqeYOukvOSqs=
			</data>
		</dict>
		<key>base1.scnassets/Animation-sun-out.scn</key>
		<dict>
			<key>hash</key>
			<data>
			yRyAiR2wCEocMipm5La2WS0um6o=
			</data>
			<key>hash2</key>
			<data>
			H9rlW0znYzntPdYh1V3ClAYgyGAw57QbgXAF+SyPgsY=
			</data>
		</dict>
		<key>base1.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			7+WgHXh/567/RY8upmlkDih6J6g=
			</data>
			<key>hash2</key>
			<data>
			lZMmEHCegXOKAcdKRW/X1T2MofaqOzuNeDlmyBCd34E=
			</data>
		</dict>
		<key>base1.scnassets/precip-particle-128.heic</key>
		<dict>
			<key>hash</key>
			<data>
			iKRpaoh23H3ZB7iA0+akkm3OOts=
			</data>
			<key>hash2</key>
			<data>
			gDK4y2s9LofGlHjcNe3Mt6OeIr3MBXrh6B2ceVmrFmE=
			</data>
		</dict>
		<key>base1.scnassets/precip-particle-16.heic</key>
		<dict>
			<key>hash</key>
			<data>
			+nUjFBklhS3TXrvtcn1fS588r9k=
			</data>
			<key>hash2</key>
			<data>
			3JmtsLAt2vGt2gozOYAcI7GKfmudfidQGySjgrMiqlU=
			</data>
		</dict>
		<key>base1.scnassets/precip-particle-32.heic</key>
		<dict>
			<key>hash</key>
			<data>
			aNkrAzVhB8MO3YEDBsSfM2f82fk=
			</data>
			<key>hash2</key>
			<data>
			VuI5dttDptbkxG29D9Wu1qQPFvkMUJECBxk785W/6O4=
			</data>
		</dict>
		<key>base1.scnassets/precip-particle-64.heic</key>
		<dict>
			<key>hash</key>
			<data>
			ddlSrGNn+NB6lA/NPnoo9tpoGSY=
			</data>
			<key>hash2</key>
			<data>
			sow9Nc5U18CbxO/09t2v96epB0EfwXoQQ8zQInLdqdA=
			</data>
		</dict>
		<key>base1Numbers.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			tTBVjGQwY64yC4QVE8JSgQqTfng=
			</data>
			<key>hash2</key>
			<data>
			7CF7RiGw4yTf9aPzOIUH2B986vozlb4NAxcEUZkrEMg=
			</data>
		</dict>
		<key>bricks-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			P1+SaemHjcyujmgUm1t/FwGxYWk=
			</data>
			<key>hash2</key>
			<data>
			ea48oycLwxivoNVnBYJDO77RZOg6fIsm5pCHkCB77Xo=
			</data>
		</dict>
		<key>bricks-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			nyzxG2TyHd5NtldIqNnUQM/aon8=
			</data>
			<key>hash2</key>
			<data>
			bCYpgLO+nN/NkKOIermt+okPuix5IISaL04OTb6Vh/M=
			</data>
		</dict>
		<key>bricks-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			VzPY0XwcukSTzgAF+CiU+gZsnxw=
			</data>
			<key>hash2</key>
			<data>
			4CXgh4wKH2hjkWh8E7lDzvUZKlak7cvih1rTegx+e68=
			</data>
		</dict>
		<key>bricks.scnassets/Main.scn</key>
		<dict>
			<key>hash</key>
			<data>
			y1CilpdudlMNRec8DY8gc7XXxKg=
			</data>
			<key>hash2</key>
			<data>
			unY5pUSKkOYaYpw1acKuqfIzeLkYneGvAkAQZzYULqc=
			</data>
		</dict>
		<key>bricks.scnassets/Models.scn</key>
		<dict>
			<key>hash</key>
			<data>
			0DI8LAkP8EhVxpwjjUIDuJyJgcY=
			</data>
			<key>hash2</key>
			<data>
			4h4j4Fo3XeEw9OWZJtjm7YT0WBPn4G4vmhzqGGUiVRQ=
			</data>
		</dict>
		<key>bricks.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			1Sp7cIb6hnI25+zLS2Vc7Ht862c=
			</data>
			<key>hash2</key>
			<data>
			1xJ2CNESddwkiXKVxwjrnYfSbHMLpjrE/+D+vczeTpY=
			</data>
		</dict>
		<key>bricks.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			9I4gIdHCSac6CG24wuh7YiPfH70=
			</data>
			<key>hash2</key>
			<data>
			ssz48cEX6F2WK7xZAJoI8NoyKwMC4rrXAZoDZ9DF3bg=
			</data>
		</dict>
		<key>cedar-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			Jd2PtxGD1MmcLk0cGgW9I9RpN4w=
			</data>
			<key>hash2</key>
			<data>
			8JuqXkNtL06AMBNi+chpJV3PlO3h0v+1OZR6tnSWpSM=
			</data>
		</dict>
		<key>cedar-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			nAOcGGMgqJqL5iNos2AutWeV3nc=
			</data>
			<key>hash2</key>
			<data>
			FNzaZMMd6KXQcXPLCXJD5d58sDA4/uFM+fdNRsckPxg=
			</data>
		</dict>
		<key>cedar-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			aHOIurzBqkzl3ksBfe8gS5fi1d8=
			</data>
			<key>hash2</key>
			<data>
			W+PxGURLULtPfo2IUTW3OItA/K/jp0dIDs6iv2BieYc=
			</data>
		</dict>
		<key>cedar.scnassets/Main.scn</key>
		<dict>
			<key>hash</key>
			<data>
			8Zu0579W99tgcdbbFFYVLVEOPLU=
			</data>
			<key>hash2</key>
			<data>
			FPGga9S2qIBX+jK6fwTdVYm0bDo66tIJMYip4KBZqpQ=
			</data>
		</dict>
		<key>cedar.scnassets/Models.scn</key>
		<dict>
			<key>hash</key>
			<data>
			iBiiWh+M6xzvohi81IELZNg98Xk=
			</data>
			<key>hash2</key>
			<data>
			WvC+1KG6dQS6+SFM0Dk7+EKVs7D1WKNhVPgk2xRZA7o=
			</data>
		</dict>
		<key>cedar.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			rH6CJJuFzZO9bGpUA4F0ooNnKbA=
			</data>
			<key>hash2</key>
			<data>
			lHM6kK8z4zCqtNL0xb4Ggqjf6HXJT6RZmzhPhc41AFE=
			</data>
		</dict>
		<key>cedar.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			Vn3SDgbUN/aA/B+eUK56WuJfxZs=
			</data>
			<key>hash2</key>
			<data>
			Sarzs3njjB1cDPVAL7O0mlwJwNCfMdRSe96d/wA8NOU=
			</data>
		</dict>
		<key>cedar.scnassets/eran-day-fog.heic</key>
		<dict>
			<key>hash</key>
			<data>
			x/mQBhpD2ygv91wTDKLN3N49OXI=
			</data>
			<key>hash2</key>
			<data>
			98rO6h7mO1O6RRogHLZulK5Qdn0DLqWe+24RMY2mDYc=
			</data>
		</dict>
		<key>cedar.scnassets/eran-night-fog.heic</key>
		<dict>
			<key>hash</key>
			<data>
			Bs/036WWOH8GNk6d/aXGBwz7HVw=
			</data>
			<key>hash2</key>
			<data>
			O7gccAQSV/ucVrBx+vwMy3XhwZWexvuDResiLZO8rOo=
			</data>
		</dict>
		<key>cedar.scnassets/eran-worm.png</key>
		<dict>
			<key>hash</key>
			<data>
			7FDjJdJansh9o0HVsiRtHDvyqk8=
			</data>
			<key>hash2</key>
			<data>
			cS3RWIClCKwoQWRpWWic5QsfDDLSZY8XXk3+TqWMyL8=
			</data>
		</dict>
		<key>chroma-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			RUupW9F8QlvVdsAGvZm4Q+D20Ko=
			</data>
			<key>hash2</key>
			<data>
			BV/Q2srFePdBEs5Jl91qt+8BaWwGG5Q1bqX4i4uCtrc=
			</data>
		</dict>
		<key>chroma-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			dtx1Kc3YLWxPaHUsznENRU9tLcQ=
			</data>
			<key>hash2</key>
			<data>
			mOjA4jAC+EA680YnkPxwjyD1dF1ChrnNbWl9u2BR1oI=
			</data>
		</dict>
		<key>chroma-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			stGwudiXtOLqJl1VTczFzTGE+ZU=
			</data>
			<key>hash2</key>
			<data>
			3HxFhNDcswPFF23Ys4d0R5okU1FP9OaxwIiyCwETrac=
			</data>
		</dict>
		<key>chroma.scnassets/Main.scn</key>
		<dict>
			<key>hash</key>
			<data>
			5Z8wdjaPcU9yFrPWNW4/lzcRAck=
			</data>
			<key>hash2</key>
			<data>
			6eN0JFif0Ki6qbNst5Fe8u2XrrPqjZGncMOUWNOyMGk=
			</data>
		</dict>
		<key>chroma.scnassets/Models.scn</key>
		<dict>
			<key>hash</key>
			<data>
			EDpcEVUc/7CMfYjGpsT/QLqZFow=
			</data>
			<key>hash2</key>
			<data>
			wJx/jM37nLD6qbJHbhWEMAlkONChvIVbPwku2D0tpCk=
			</data>
		</dict>
		<key>chroma.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			TNLh2nk7Ukx7Xi7DQFN55ANCLWE=
			</data>
			<key>hash2</key>
			<data>
			wc79jW/TDvsnWntxbGUJ/A2Uka+jN8dyVc9nQ83ns0s=
			</data>
		</dict>
		<key>chroma.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			EFT64sq4FVl7+9ZS4tizQHqIiHE=
			</data>
			<key>hash2</key>
			<data>
			8RP4GAX9lV5nD8RzGjRnZ35HlSniGW2QBSsXDkXc5ys=
			</data>
		</dict>
		<key>chroma.scnassets/oilslick-rainbow-8.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			rNewckxfsOPDSv6BhgbyJgsoCow=
			</data>
			<key>hash2</key>
			<data>
			tF23984QGbEYnrvkwxt6RKu4H2fu8BAmrWaL10wpvEs=
			</data>
		</dict>
		<key>cloudburst-title.json</key>
		<dict>
			<key>hash</key>
			<data>
			V6QxkX06eJUy43WJJ4ZCskY3E2M=
			</data>
			<key>hash2</key>
			<data>
			Z2So1EAaxRpQIOWYFBi8mRbPkRt/jAh60wDW3Dwd7es=
			</data>
		</dict>
		<key>de.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Et+yk0iKrrascmsp1VYwUwb4aHY=
			</data>
			<key>hash2</key>
			<data>
			qOimdEjOC3cFvqSYPZV52HvaBGAU8KvFeZHFt5LOg8c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>de.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LTDON6YEF7ST70ahhzpRlX+dllo=
			</data>
			<key>hash2</key>
			<data>
			pATwe6R/mTyfKctJaAAwaMfm3K4DaD96V6geODZVMeo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>default.metallib</key>
		<dict>
			<key>hash</key>
			<data>
			dsXAdWS6cd9GJ7vas5TsxhoFuDg=
			</data>
			<key>hash2</key>
			<data>
			JAwcrPM0pedRTuV60ND/BR3d3CStkkShtuAgc25sOAw=
			</data>
		</dict>
		<key>depth-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			i6AHSjdO9nTgEpGKiYpKnCwd47E=
			</data>
			<key>hash2</key>
			<data>
			b3UnZq8mbt+dSiQzQVugRFiau7z8bn/T/nIN6pnGCcY=
			</data>
		</dict>
		<key>depth-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			PqAdyFaK6bIPty7YiB1cXxv7owc=
			</data>
			<key>hash2</key>
			<data>
			coaBrUG2i1+9Y/1MSHiQxJPtNktsaqZXcsd+3oOUo+Y=
			</data>
		</dict>
		<key>depth-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			yZ1JyHEvjpSoiYQc9jN7FD+49CA=
			</data>
			<key>hash2</key>
			<data>
			UszUnMNaUUEjX2SRUeXYYNVQkuayEphJNsZgmw30Pj0=
			</data>
		</dict>
		<key>depth.scnassets/Main.scn</key>
		<dict>
			<key>hash</key>
			<data>
			BrvyBkR9/vEo1Jf0XwyDua89w9A=
			</data>
			<key>hash2</key>
			<data>
			xx+JnnLW3Y52FFNvkjdRgr/tDVP55puUwfwutuHTXOk=
			</data>
		</dict>
		<key>depth.scnassets/Models.scn</key>
		<dict>
			<key>hash</key>
			<data>
			SJBpfxO9RnURG4lsYrlTFamj/Ik=
			</data>
			<key>hash2</key>
			<data>
			rk4EYnprZdDA7yIGKEpmDDq9yIVhCXnbsCwkotV4HMM=
			</data>
		</dict>
		<key>depth.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			HvUZQ/SLKOk93mCvX9E8DYnjypc=
			</data>
			<key>hash2</key>
			<data>
			gmhYD0v405vHfobwzRgmdkMGqlFrDCWzy12XV+PYgWI=
			</data>
		</dict>
		<key>depth.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			zkcwo0Qk+stak39qxKMUNzC1SAY=
			</data>
			<key>hash2</key>
			<data>
			2XKVNIUk2JxtgBDqRnN11HobyT6uhi2cjCJvmUjVJUA=
			</data>
		</dict>
		<key>detailed-enter-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			c2JMnEGV8Gbk8qnjnukHmoUsCnQ=
			</data>
			<key>hash2</key>
			<data>
			0chQYwVT4adzxLXWseBjEWF4GJQUSAbpm40uzlht1mc=
			</data>
		</dict>
		<key>detailed-exit-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			MFWyiESvY04BB6mME+Wl/LIYlY4=
			</data>
			<key>hash2</key>
			<data>
			mObv7ynsEnsuMS5ANb/ywTHVnDPE5jnwjKJ2z+52h5c=
			</data>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			M2SSnM+6MeTDWE5Q6p1bQBVsuE8=
			</data>
			<key>hash2</key>
			<data>
			n3XM15JpqeEO6HSwBoWX16AMxcyMPCoPeJokY/RkipM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/EHBdA5VdycM64mhhD4lJJsUVP4=
			</data>
			<key>hash2</key>
			<data>
			9aFIjTuVS8FbF2Z8ugiePol2qgBZ5UcGhyKDfXAoDfM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>environment-13.png</key>
		<dict>
			<key>hash</key>
			<data>
			tRZts3Mbk9wSvWjulvuJgkhvSUg=
			</data>
			<key>hash2</key>
			<data>
			BVOlbKrcVO6uWeDuv4G8FdxJzyN1STxo/0vf9mvueNY=
			</data>
		</dict>
		<key>es.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			l7qkTcmA0PTxu6o11poUXHig7Ak=
			</data>
			<key>hash2</key>
			<data>
			kqfI+wpCGAiJ2bkTWFMeHXEBARUqJeKyp5zszl+EO0M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>es.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mSmC+D9j02gAPc1cwywbssQiBKQ=
			</data>
			<key>hash2</key>
			<data>
			tQWvVjd1KS9WQkf6ZZi78V4cdDFoKqdPfcwIGC2h9Do=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>explode-close.ahap</key>
		<dict>
			<key>hash</key>
			<data>
			TQy52pumlxm1scYQHd6eum07DIY=
			</data>
			<key>hash2</key>
			<data>
			ufHbZDR/b3BGY25kK9J8PKBMOpoQt2cMmyOmCZMStzE=
			</data>
		</dict>
		<key>explode-enter-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			3rdngWVM5LA84UFjlWca4WPdhiM=
			</data>
			<key>hash2</key>
			<data>
			1rlWOm2HzhmNnQmOLY6PJAZOlEhpqBB7Kh/7mybZLN4=
			</data>
		</dict>
		<key>explode-open.ahap</key>
		<dict>
			<key>hash</key>
			<data>
			VAAR35KfKPUX5E+tFwedxZlNJUo=
			</data>
			<key>hash2</key>
			<data>
			SwGgC+ZQcEOmSzh8XWviqiQWstN8FCM4UzIi6SYtdgw=
			</data>
		</dict>
		<key>fr.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+go7sbv5QjGL7m4u42RMrw1kDB8=
			</data>
			<key>hash2</key>
			<data>
			fptiRsWUkznjAYjPUX7tMavfPvcTNnMzkLASWIjBJZI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>fr.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cVBkX8o0wGEzHC1mqOzEvPkmlLU=
			</data>
			<key>hash2</key>
			<data>
			upAO4eVipBmjyPr6EYVjhrBVMfDmsBNdtmZcHdUrERE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>graphite-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			YR7wQ7O/RhHvqBigzewKV/syQZQ=
			</data>
			<key>hash2</key>
			<data>
			fCox5ozYoYyHRaHgZMtoFRuz+bM+/nvINF8B+B8qXjo=
			</data>
		</dict>
		<key>graphite-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			np7EJs3SHyGXbeA+fX/cfEzy480=
			</data>
			<key>hash2</key>
			<data>
			rgbkUPziEcNyZRpVRXOIlOYwWbtOXtYkhzEJK7H0OeU=
			</data>
		</dict>
		<key>graphite-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			LqeVMAoHG+oCR5NyV/+9Hz4QJBM=
			</data>
			<key>hash2</key>
			<data>
			LTdtVyCi/S7n7PUBMXqihP6MQxFAUb0gYdH5z5EQk4E=
			</data>
		</dict>
		<key>graphite.scnassets/Main.scn</key>
		<dict>
			<key>hash</key>
			<data>
			LA1AKuAHT6pD/0akIJp83KH1vLI=
			</data>
			<key>hash2</key>
			<data>
			WZaURBobzL/Co8wPAvqblML4iQjhzxB+gpskfqtPKFI=
			</data>
		</dict>
		<key>graphite.scnassets/Models.scn</key>
		<dict>
			<key>hash</key>
			<data>
			6NLjOxCgx72BNWjbn9sf7rsX5Ck=
			</data>
			<key>hash2</key>
			<data>
			xjMgZEb0aY+7qdu+FZo+kmHpjG61RRGxI6HPUQnFaVY=
			</data>
		</dict>
		<key>graphite.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			cC0LQlLYrXVpTgqJZxMTMRjkKFE=
			</data>
			<key>hash2</key>
			<data>
			M4KeG25jEvnMssah6wVUSHzEqj1E6HqIfRaGcVOVi8c=
			</data>
		</dict>
		<key>graphite.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			35J9Rn89XIH5jJC21gQQRJc/MoI=
			</data>
			<key>hash2</key>
			<data>
			UVpMS7DE3S+eUv4IEpRZZfIdy8GsbDKw7rPELg6+ARU=
			</data>
		</dict>
		<key>it.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4vM+KD0cvtYf4JyLml1D60EXViA=
			</data>
			<key>hash2</key>
			<data>
			idxx3kwVLwzh0PXKsl0srNRr3Rpdmi98jY7EGvEHMmk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>it.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2P+PjoufHmOEDNu336ve6rqLDAM=
			</data>
			<key>hash2</key>
			<data>
			L0NtRL12UKi/IFxM5VW5yZgXCd1cCx3UK0Dsfu954Og=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2C7ii5Kv4vKxRjCjf3Kp5BZ1vAY=
			</data>
			<key>hash2</key>
			<data>
			guxROHcfd6epLmy4lJSo+WjEQAOQhnGnL3BS+3Bcj9k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ja.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nELECNjh/DXfP4ElOEfZe7X4XIA=
			</data>
			<key>hash2</key>
			<data>
			zt8A4MrLCXRdOSdYL9JhCI65eY+HVMgXhhwuvUfCdmc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>karat-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			gAGkywTUQ2hFODLtmtC7gEwXP3c=
			</data>
			<key>hash2</key>
			<data>
			2Eba/ijovSgWZZGF7v+WsY3+qDfppJeTpcVE2lx/CqY=
			</data>
		</dict>
		<key>karat-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			+Mq70YusyQAn+9A4Y1D2F4AMigs=
			</data>
			<key>hash2</key>
			<data>
			wJX5kmWVgr5QeOsBS6LaBJE3kKqJ2lOpa/bxix5ToWw=
			</data>
		</dict>
		<key>karat-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			ys3gO/H606017EQix6ZrYbu97yA=
			</data>
			<key>hash2</key>
			<data>
			KxybT7xdC9+uRg1eGiPSiPUAXzBf8YFXeq8UyF3ZgFo=
			</data>
		</dict>
		<key>karat.scnassets/Main.scn</key>
		<dict>
			<key>hash</key>
			<data>
			XhWuDhJYOijrhC5FCDFR94XwOEM=
			</data>
			<key>hash2</key>
			<data>
			yTOVC6KiuMvLN+W9zb4D+Lu3Qb0Adyq0ReK2/qZeXSA=
			</data>
		</dict>
		<key>karat.scnassets/Models.scn</key>
		<dict>
			<key>hash</key>
			<data>
			Z18C/Ep2cz7DgLr1ZaELLDfsg9Y=
			</data>
			<key>hash2</key>
			<data>
			OlyNbmRSDDpu/8eQ0BxsxVZ70NbJhHpeyg1ilWrs4mI=
			</data>
		</dict>
		<key>karat.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			qdWExAJjhOkis8H+FFQJzdmQcxg=
			</data>
			<key>hash2</key>
			<data>
			VCfR/7bVj+OH045LDnPQu/N0O+zFcZSEFvmhG1eGU7g=
			</data>
		</dict>
		<key>karat.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			j19DBwsvvPHh3ErSQmw3BnGgUrg=
			</data>
			<key>hash2</key>
			<data>
			sYxzJ/04Ow/xAwqarrJDYMWONfPzqb81OmKlyx5Qn6g=
			</data>
		</dict>
		<key>karat.scnassets/gold-dim.heic</key>
		<dict>
			<key>hash</key>
			<data>
			8UPb0hBIgCf61XXfWwwBPWz8asw=
			</data>
			<key>hash2</key>
			<data>
			1pnPER0Tv699Zj2ahvcilRtZgJHQChxdNIsbpCBgzKQ=
			</data>
		</dict>
		<key>karat.scnassets/terrazzo-mask.heic</key>
		<dict>
			<key>hash</key>
			<data>
			1tRCklUlUxa2RZ8bEskhPDO8IME=
			</data>
			<key>hash2</key>
			<data>
			AcoGnwqqU4XABPxBMbCI47lQNoszcxZIZyCcaPtlxp4=
			</data>
		</dict>
		<key>ko.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			w5J6bqb2u5ubRlejwbWpP9GnwHg=
			</data>
			<key>hash2</key>
			<data>
			/KT5m7nMufPHKjZZcRQNNxPvjhDFT5ZRgO6LKh5To5I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>ko.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/wuFqBA3/wO0M0tKH1oW+Qlir9Q=
			</data>
			<key>hash2</key>
			<data>
			LTi1Q8RTDCQ8VLxoCadK5RZAr6ZrXz5a+v+DfjLQLds=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>membership.scnassets/back-sticker-reflective.heic</key>
		<dict>
			<key>hash</key>
			<data>
			jamLeUp1+TQE6e939lca+Ehovrk=
			</data>
			<key>hash2</key>
			<data>
			/8HbOvINvxdV0dmErlaHmoz7Pv13uLv8Uiv+SqWoXNU=
			</data>
		</dict>
		<key>membership.scnassets/black-reflective-6.heic</key>
		<dict>
			<key>hash</key>
			<data>
			1oZLz+l7q44MOgZ9OuKVVUs3p+A=
			</data>
			<key>hash2</key>
			<data>
			aQgIxLaCCMGUQ6KJJj7oKs4Tdzf2EgHKlqWKuaTMBdY=
			</data>
		</dict>
		<key>membership.scnassets/coin-normal.heic</key>
		<dict>
			<key>hash</key>
			<data>
			OoVCKjAt+VKhb3wlV9CXne15kW0=
			</data>
			<key>hash2</key>
			<data>
			zJcxBjApzF+4JcUxnvdsfEkFhINuRaoa0quJCnxOkHA=
			</data>
		</dict>
		<key>membership.scnassets/environment-13.heic</key>
		<dict>
			<key>hash</key>
			<data>
			yxj82kveuGE49NrF4yrXsGfrucQ=
			</data>
			<key>hash2</key>
			<data>
			yrv827MBf7ySPY+H3nHEXFlTpnSg7Apwz6Z/Z4qgmrA=
			</data>
		</dict>
		<key>membership.scnassets/gold-3.heic</key>
		<dict>
			<key>hash</key>
			<data>
			s1kBIuvRszyM2dVIaUzB7aUv8CE=
			</data>
			<key>hash2</key>
			<data>
			Y5nYppE9nqylw0N5lftrnQFYsl5JT0yEAZl+Z9kycYQ=
			</data>
		</dict>
		<key>membership.scnassets/member-card.scn</key>
		<dict>
			<key>hash</key>
			<data>
			lTTBorixMizq32hjN1FMIeEFRhk=
			</data>
			<key>hash2</key>
			<data>
			fFSjbsMVQ8hEr4G4LorcoOqhmjkNtACxK2XhHFNlMwc=
			</data>
		</dict>
		<key>membership.scnassets/particle-sparkle-64.heic</key>
		<dict>
			<key>hash</key>
			<data>
			y2CX2EPFnvva/8exJ9kvY1fd2uc=
			</data>
			<key>hash2</key>
			<data>
			ahOgf/ZFGoOifwVFSybeXAgNA5lqOGOXVYGoeDgUp+o=
			</data>
		</dict>
		<key>membership.scnassets/patron-reflective-2.heic</key>
		<dict>
			<key>hash</key>
			<data>
			D1sEpw2WetMYijwgaWyBKWDaUn8=
			</data>
			<key>hash2</key>
			<data>
			0HlUNJTGXMPFd1aTFxIcH9zZDgOqiJqZFZuyfRyy3yk=
			</data>
		</dict>
		<key>membership.scnassets/unlock.caf</key>
		<dict>
			<key>hash</key>
			<data>
			2ZB+fUN1SPtlV9JA55lUzA8a9Fc=
			</data>
			<key>hash2</key>
			<data>
			Si3ufJy9HYx6NyJtztpweLyPib9paDZHp8P5fJsBrcE=
			</data>
		</dict>
		<key>menu-close-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			Xw598IULhI4yrLlJJ7rsGfo4Sk0=
			</data>
			<key>hash2</key>
			<data>
			7flDoujBSbIlcdGniIBGicsQfaGN78ZMX9sCLIyuuyk=
			</data>
		</dict>
		<key>menu-open-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			rusJyBRbKCB+AbouBTUltwQ1/M8=
			</data>
			<key>hash2</key>
			<data>
			mLZ4UtPYZJQF83fze1lPgXK4q1/AaheSIE9xwkK+CEQ=
			</data>
		</dict>
		<key>model-change-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			Hzbaq2zoQ5QESa1bB5OrCWNzY3Y=
			</data>
			<key>hash2</key>
			<data>
			yRHFo12id/Nof+geZkPVcmWLP6ud1FLxAK5GR2m+nRo=
			</data>
		</dict>
		<key>model-change-2.caf</key>
		<dict>
			<key>hash</key>
			<data>
			Gabb5Iej/gM1AKV6UThji/QEveM=
			</data>
			<key>hash2</key>
			<data>
			9rv6qiRVqZ2Stvn+zNhtGUfMaNgKBDVZNNtWzxgMfo8=
			</data>
		</dict>
		<key>model-change-3.caf</key>
		<dict>
			<key>hash</key>
			<data>
			DxJ2TioIZy8nLa62cLJvQmWzFmc=
			</data>
			<key>hash2</key>
			<data>
			2anBq9U1aA+TFpMMyA2CAHRnMAv6e1c7o4BmEWhcbW4=
			</data>
		</dict>
		<key>monster-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			aEtpqqeq25BOAsESeZjUYMwK1YI=
			</data>
			<key>hash2</key>
			<data>
			QbpXpi9DTr1pFU5MvibHHo3z7GoLVtjXfUB3hlfJdho=
			</data>
		</dict>
		<key>monster-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			NItWhVRF0e5NKx61tLhQsS+JVgs=
			</data>
			<key>hash2</key>
			<data>
			dLFA1KF2NvvxH3bU6Akqp+psUyMVL7Fef1Qc3KpK1sk=
			</data>
		</dict>
		<key>monster-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			OTuCV/89NlfbPugIWset1pcmTAk=
			</data>
			<key>hash2</key>
			<data>
			z/j2djFJ78/Z44NBRiIeTajU1RNgUQtT5L8+AFiwPGA=
			</data>
		</dict>
		<key>monster.scnassets/Main.scn</key>
		<dict>
			<key>hash</key>
			<data>
			ogk4RUnke1BGkVLy0ATYDOD4mQ4=
			</data>
			<key>hash2</key>
			<data>
			Z+3tGHvGZqY9vj3oT4enejDzUWy9dWkQiooVsKaq3gQ=
			</data>
		</dict>
		<key>monster.scnassets/Models.scn</key>
		<dict>
			<key>hash</key>
			<data>
			0Ba/GPX2xxNyXKPxbv8KGAlhBvA=
			</data>
			<key>hash2</key>
			<data>
			VWzHhubIqyyMDqFbaINle+6zDZyACoMNBF3nN9xt5iM=
			</data>
		</dict>
		<key>monster.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			aWV3BDPe7CbUgmY4Y1+VbLUaUE4=
			</data>
			<key>hash2</key>
			<data>
			TxMGja8CXElU538uasHY+/o94Ir+BD+nGMdulQvTV/4=
			</data>
		</dict>
		<key>monster.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			DUwZpvjGFYfm0RJaUCyawgjpNlM=
			</data>
			<key>hash2</key>
			<data>
			lAB6uGfaEtsvd9jRY5z0VLDAxoaPh4s2nXXyI/xOrD4=
			</data>
		</dict>
		<key>moon-tapped-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			OPziTjYzgq109RTL8UEp2E3pvy8=
			</data>
			<key>hash2</key>
			<data>
			ETiEe+S5zvvuWbuu65vFhq85TromXdcrk3Ebt0PtvtE=
			</data>
		</dict>
		<key>moon-tapped-2.caf</key>
		<dict>
			<key>hash</key>
			<data>
			6mFHhVZiroGI6Lw2AH8KAZXoZTc=
			</data>
			<key>hash2</key>
			<data>
			/5yWxEW7BvETzTOMmWtO66tROzPJpEX3pc0HVmdvXpQ=
			</data>
		</dict>
		<key>moon-tapped-3.caf</key>
		<dict>
			<key>hash</key>
			<data>
			ZO5OUREEO++Kagw9uV8dSbc0KI8=
			</data>
			<key>hash2</key>
			<data>
			SlCm3fyM2xdvc4xPt0ls9bCA70luxyAEPbP6OvLJNI4=
			</data>
		</dict>
		<key>moon-tapped-4.caf</key>
		<dict>
			<key>hash</key>
			<data>
			SL5N04Q17i7eGhR2u5g4+jMY4so=
			</data>
			<key>hash2</key>
			<data>
			psoeAd2QUnTMNj0MgAySf8mcsaCbvGE2LdZDjperhlU=
			</data>
		</dict>
		<key>moon-tapped-5.caf</key>
		<dict>
			<key>hash</key>
			<data>
			dpUUDGWveOVFg5GXWvGJmkVPJt0=
			</data>
			<key>hash2</key>
			<data>
			NBd5+RkQ09IxGbqcx6fMOi0ZDXOMeRSLT99hvzYOOJs=
			</data>
		</dict>
		<key>noise-normal-256.png</key>
		<dict>
			<key>hash</key>
			<data>
			XXXN6dGFLmwHWB3noW2ZHdckl4I=
			</data>
			<key>hash2</key>
			<data>
			KmVvUhcMtK42NASS6GHF0JcFSgP92gxM2yZ3bnPlss8=
			</data>
		</dict>
		<key>noise-perlin-256.png</key>
		<dict>
			<key>hash</key>
			<data>
			Qqy+1iZEZJOv0vldaeyJbqKdRWw=
			</data>
			<key>hash2</key>
			<data>
			Lv7wzBuyobFkgzxK7vqE2hn5jP+XrL0C05d+/HxW3oM=
			</data>
		</dict>
		<key>normal-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			cf3RwXmcWshBcK2KLXyob6vO1j0=
			</data>
			<key>hash2</key>
			<data>
			pA7/nksYB+eZDDheRAgP1V5cptq9cqjvp6sGeFBoME4=
			</data>
		</dict>
		<key>normal-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			8YiWkSvW0zx9OVrCiilifY6iCCQ=
			</data>
			<key>hash2</key>
			<data>
			gI0frTxtP3iGd/e0g6mZntjcpBECMexDv/qeZw1a4+4=
			</data>
		</dict>
		<key>normal-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			E7YQDPKEN6UPLK8vqrW7ynr5N6U=
			</data>
			<key>hash2</key>
			<data>
			Mzj/NgiKLhZPBEdKfhNGlUerlAQsisuwoLchKTqO+RU=
			</data>
		</dict>
		<key>normal.scnassets/Main.scn</key>
		<dict>
			<key>hash</key>
			<data>
			9FsF/YAGaMLP1AHnki9fYyXBvMc=
			</data>
			<key>hash2</key>
			<data>
			pZfbWhrwYGXOrQbdCzHQY1tbUTDgFck9qleCozGQZNA=
			</data>
		</dict>
		<key>normal.scnassets/Models.scn</key>
		<dict>
			<key>hash</key>
			<data>
			QJVwltjr0CLuwjWeO3eqbo9Hc7k=
			</data>
			<key>hash2</key>
			<data>
			MY19qiVwkJIO/tdUnfBRSswDMUMd8Un68sVlN+DwhFM=
			</data>
		</dict>
		<key>normal.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			9bT9ZC0qb30QXWc3NcMf7+CZtQI=
			</data>
			<key>hash2</key>
			<data>
			UMtPQgVHvvCbglhsxCIp4NI8HYgHpobZmx2/SegFEvo=
			</data>
		</dict>
		<key>normal.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			gaiGR0kKYwBmRcs7t/qrD3EyD1U=
			</data>
			<key>hash2</key>
			<data>
			iaGlaOSqqwklTYq/xDMNPQqtQQja2vmfVlfELK3VwOM=
			</data>
		</dict>
		<key>opal-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			JxA+QKOggC+LfCpsyltQYX8TGX8=
			</data>
			<key>hash2</key>
			<data>
			ozcv7rePcJwJE3xSEIzfY9WeuXcO+us0ezzZzZ4RAVM=
			</data>
		</dict>
		<key>opal-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			lRY8D0TWIc8/rI6FvomnxAdGv/w=
			</data>
			<key>hash2</key>
			<data>
			BjESPbFkNIrLSX/nSRYhCFy2HK6CW6gEti0/IdcQKeI=
			</data>
		</dict>
		<key>opal-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			W8U9oQe4l5m28/gHjL8ZjtVvpuc=
			</data>
			<key>hash2</key>
			<data>
			GSLwZA4IZQD8s2R3zUDJhr0fKp/zJNmwBzKHSGlklPQ=
			</data>
		</dict>
		<key>opal.scnassets/Main.scn</key>
		<dict>
			<key>hash</key>
			<data>
			yebSzAR3TCjLfr9FMJso4Zx+txM=
			</data>
			<key>hash2</key>
			<data>
			+D0DN5o38//BJWjYgTNwvVmmMTE2Uiyo0P1o2eMj2no=
			</data>
		</dict>
		<key>opal.scnassets/Models.scn</key>
		<dict>
			<key>hash</key>
			<data>
			q0eSE7RKQrASQluW7oHz+XFcjVc=
			</data>
			<key>hash2</key>
			<data>
			8B6pp/rljzEHEKc7sCzIrG2Ug/XwbyQUcT+lYp1o8Z4=
			</data>
		</dict>
		<key>opal.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			dyWirefEnHf7x4BueemkNz8jrI4=
			</data>
			<key>hash2</key>
			<data>
			8VJlFsuH4B0X/pCmYIwxmLolOWMzv17n3ZmwDopkSJQ=
			</data>
		</dict>
		<key>opal.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			aGrGF3llV4T9b/olSOuFZR6TLHY=
			</data>
			<key>hash2</key>
			<data>
			WqtOxKlXNFt+90nSllSdconArn95NTNipr0dHYTY9u0=
			</data>
		</dict>
		<key>pieday-crust.heic</key>
		<dict>
			<key>hash</key>
			<data>
			Jp6B07R/dnmAl4LEssmet73/HLE=
			</data>
			<key>hash2</key>
			<data>
			FK82v2+YJLOQhntR/7gt1rq+60C6EAP6crmLY1HP/2s=
			</data>
		</dict>
		<key>pieday-filling.heic</key>
		<dict>
			<key>hash</key>
			<data>
			IekqFYSfMIldu2LVvqGt8njjVtU=
			</data>
			<key>hash2</key>
			<data>
			Hyzzuly3VAIB/Os/nN7TwumW/Oc4qJVNxLSUWB4Qk7g=
			</data>
		</dict>
		<key>presstube-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			Jh/2brca/CFAkquNIrJuhQdDC9g=
			</data>
			<key>hash2</key>
			<data>
			dBF50qjDxBrBPguAOunECVipbRsoiOg3syhs5ql/lo8=
			</data>
		</dict>
		<key>presstube-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			hRGtshy6vCyOXnkqMXig+f4hvY8=
			</data>
			<key>hash2</key>
			<data>
			CHZfpy+4QzL3sxDr9S4a6e2lOqbaVjRMQDTFBApOa7w=
			</data>
		</dict>
		<key>presstube-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			P8bZldX26ircgfJIKVyDMzuYeK8=
			</data>
			<key>hash2</key>
			<data>
			yL6tFK1dIJsgm502PRAkP10FN4xGPIbmqbOeJlWhRWI=
			</data>
		</dict>
		<key>presstube.scnassets/0-04-13-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			EjePHxKiw+FQ2V7D9Yb1dMMRQKQ=
			</data>
			<key>hash2</key>
			<data>
			eTujK6ZYaYzBZ2Y5Sy2Rt44S46F8qo9k3qKulnerxz0=
			</data>
		</dict>
		<key>presstube.scnassets/0-04-13.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			kntj13+5eswQBuu0miQZJCFDj2g=
			</data>
			<key>hash2</key>
			<data>
			Rs836+TG99dYvHCQkxO2w6D/eakjUeATXIhIKJjGThA=
			</data>
		</dict>
		<key>presstube.scnassets/1-04-13-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			teuex54bKtZSP6cWrzpxQiPZ27E=
			</data>
			<key>hash2</key>
			<data>
			Tg9oYz/t221iGNwp8+H30uXFKHxH4uXZfUlrL7wL1oI=
			</data>
		</dict>
		<key>presstube.scnassets/1-04-13.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			TDa84alcKy0kFzyZi/n3h+xOF04=
			</data>
			<key>hash2</key>
			<data>
			iEaFKlJMOtSW5k1iTSRDsn/vTbZolOQCafIHZdlv+X8=
			</data>
		</dict>
		<key>presstube.scnassets/2-04-13-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			H12WhzmWCmyLJKYzlYLy5ItY4iM=
			</data>
			<key>hash2</key>
			<data>
			pzozz4vA7Fz87BRzB6//H9WSjfqDlXzNlBfanm2hK3E=
			</data>
		</dict>
		<key>presstube.scnassets/2-04-13.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			VKTgat3E9lDAYZbc4+JCNWHEvN4=
			</data>
			<key>hash2</key>
			<data>
			6GPIqGxeVnlomzuAvucsOOC70vFx6iig9Hsr8sT3c0Q=
			</data>
		</dict>
		<key>presstube.scnassets/3-04-13-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			eI4Ee+J19KEpPGerIzDXnudraZA=
			</data>
			<key>hash2</key>
			<data>
			8TcCD0qllb6CcXDUI98hmESFo17TiAxAtDd4QLgYYkQ=
			</data>
		</dict>
		<key>presstube.scnassets/3-04-13.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			DDZPZzZs8HnsoUI8VKBCZqITqGw=
			</data>
			<key>hash2</key>
			<data>
			fQP1l74ijICGx3Z/3PCbhPFzol9SAzFtq/AW+Dda45c=
			</data>
		</dict>
		<key>presstube.scnassets/4-04-13-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			L90k627K7cPguk1ClPkAjAWgWAE=
			</data>
			<key>hash2</key>
			<data>
			zQkfH7Sh6H54xvRg1FAJwC8lQWb2CGt+Y6BEReSfjFk=
			</data>
		</dict>
		<key>presstube.scnassets/4-04-13.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			PClVaFrA+GCHuxj4DrAbDBmgeVE=
			</data>
			<key>hash2</key>
			<data>
			elJupOOs7B7H09hAQw3TL1qvP8bCk8u3z0AHlGqR6HA=
			</data>
		</dict>
		<key>presstube.scnassets/5-04-13-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			/hPJD1hqH8s7Au+Sdx7KcfmoxCM=
			</data>
			<key>hash2</key>
			<data>
			iSB8l5+Mrwy9qN8wdI2F5a+OmHKEInGfO2reb1nMBIM=
			</data>
		</dict>
		<key>presstube.scnassets/5-04-13.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			7BIyy+zKWTN7nZMFuaCDaDYpAqM=
			</data>
			<key>hash2</key>
			<data>
			fCb6YyW2Ej2MX6ML5q7xmVRSv01Q0uEWrqE2x2OroZ0=
			</data>
		</dict>
		<key>presstube.scnassets/6-04-13-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			ch73Ef1hV8OG2m1qLmsvhQdDVqs=
			</data>
			<key>hash2</key>
			<data>
			Kkz3weXDFD2M67GBWnFXbpqKGcnwZ0svL457qgn+nDE=
			</data>
		</dict>
		<key>presstube.scnassets/6-04-13.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			mZY9rMtHyfIQnkHRl8fSoednCJw=
			</data>
			<key>hash2</key>
			<data>
			nGOdZkydOylMp5zjFppssL7gSaZX7C0tE4a+GTNi4R8=
			</data>
		</dict>
		<key>presstube.scnassets/7-04-13-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			qoXtF/1bPQ+1KOutvS2T3DugYFk=
			</data>
			<key>hash2</key>
			<data>
			GXJIhP+x1Vw4WnPtOEcxpwHaCxrjVoilzyRqNziRDNM=
			</data>
		</dict>
		<key>presstube.scnassets/7-04-13.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			HxWDsIMZy8XR/CkeJgrwS/eoReA=
			</data>
			<key>hash2</key>
			<data>
			XKbC7Zq7aRVjRw9neRz6yNpHgFclAv3PLYecuEzvOWc=
			</data>
		</dict>
		<key>presstube.scnassets/8-04-13-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			1eyQmJpfMxeollGVQlUtf243oWY=
			</data>
			<key>hash2</key>
			<data>
			xyVKFVPP+PmrUXLiXL1WH4blMGUqSw+GXiE9aITxKnU=
			</data>
		</dict>
		<key>presstube.scnassets/8-04-13.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			L02ZmtKoT/wlIB2anFtyILCwzmo=
			</data>
			<key>hash2</key>
			<data>
			nc7Wo4euDlK4CQ598el76KVumAqBvMUAS2kor67mikk=
			</data>
		</dict>
		<key>presstube.scnassets/9-04-13-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			8jib6lNzU4DC6gDSgjp92YHdwyE=
			</data>
			<key>hash2</key>
			<data>
			hTcPYUXXIa3jf6/whHC6g+wc+M6biqAi0MhS0KiIdbg=
			</data>
		</dict>
		<key>presstube.scnassets/9-04-13.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			ljsTHvpxFaTJUkPlPA95okIfFjc=
			</data>
			<key>hash2</key>
			<data>
			Y4VdTdPUOiE/Lr8WhuipRNsw7KLVJVj4PnVNTBm9jEU=
			</data>
		</dict>
		<key>presstube.scnassets/Main.scn</key>
		<dict>
			<key>hash</key>
			<data>
			FJYmr97qzVionip31f9x0ZftXPc=
			</data>
			<key>hash2</key>
			<data>
			gW29iGeVMbm8yLq7BS8rgS6BRhl1LYnC8llZosBTzjs=
			</data>
		</dict>
		<key>presstube.scnassets/Models.scn</key>
		<dict>
			<key>hash</key>
			<data>
			0jEIAApMXf2Gb2B87grpLyUb9Rs=
			</data>
			<key>hash2</key>
			<data>
			r8P/G7Gjd81Cj+uDPVFyD43+a9tjdM9HaNum2Yr4rjI=
			</data>
		</dict>
		<key>presstube.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			LhyLueWOxw9opuWM+xMd/KIwt/c=
			</data>
			<key>hash2</key>
			<data>
			WX8RPqbZaEt8d52xYaEiWIsIH7ZgD/EwfAKRPh6DueI=
			</data>
		</dict>
		<key>presstube.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			nVMeaDq7NA/6Ev7Ww46iirdaKAk=
			</data>
			<key>hash2</key>
			<data>
			EA3zz44lIyL4wkPEMbQX05akZHaJ+QovfQ63X8KILxE=
			</data>
		</dict>
		<key>presstube.scnassets/comma-04-26-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			q2A7GaLLfib0Zu8xlUz6cSZfBys=
			</data>
			<key>hash2</key>
			<data>
			oPz0SiJu0+jhfadacOEg04Qg8z2TQhjcEFPmHtSwILQ=
			</data>
		</dict>
		<key>presstube.scnassets/comma-04-26.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			jIy4YLed2p1Uk4kBIzn2r59XqeU=
			</data>
			<key>hash2</key>
			<data>
			PkbmpfD7BKO7rFK0vP8irCM3iyytGuHByOxCM75NDJ4=
			</data>
		</dict>
		<key>presstube.scnassets/minus-04-26-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			DfU1K6WGKmR49QaTACxqpieCbSE=
			</data>
			<key>hash2</key>
			<data>
			d/HZXCtWGMHDs/whJD/zD7oKcjX7+oI9tNjhRFhcZYY=
			</data>
		</dict>
		<key>presstube.scnassets/minus-04-26.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			1Ul7qLEkBCzl2TD+EXuyc0gO3AA=
			</data>
			<key>hash2</key>
			<data>
			Q6yC8Emm38jBYlCIZBpJe6nMWY/gWYNOT1dpf1pyx7s=
			</data>
		</dict>
		<key>presstube.scnassets/perc-04-26-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			gJgmW9Mj5JHZe+zsCelyLNh+MjE=
			</data>
			<key>hash2</key>
			<data>
			Yh59KgjFYgKaYxZR8YbUwgPQr3QhgxMdDb6+HFzTVGk=
			</data>
		</dict>
		<key>presstube.scnassets/perc-04-26.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			5nE/buAnySgcHp2JAHvs9bNX6k4=
			</data>
			<key>hash2</key>
			<data>
			gZnQbAxDRKk1VkFoZTQFfmXi8isNFzWti1eOpAeCDt0=
			</data>
		</dict>
		<key>presstube.scnassets/period-04-26-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			1OcQxNWlurJmELq7rhU2IGvpRw8=
			</data>
			<key>hash2</key>
			<data>
			uYKGTW2edXXfPNnlBQ5EReSCclonW+pVngLfNR2uik0=
			</data>
		</dict>
		<key>presstube.scnassets/period-04-26.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			eZyB56uzc2sc3bYZR9PqMl6cyc8=
			</data>
			<key>hash2</key>
			<data>
			/vi/MEuDLBTQmqzxnCojP7BY9QVDGfqDP+3ncBRPBx0=
			</data>
		</dict>
		<key>presstube.scnassets/plus-04-26-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			vviEI1qPeEmGrhQdqjnWPNVovnE=
			</data>
			<key>hash2</key>
			<data>
			Z6wrJmBZU62aXVnh99+fB4XXvn9a0z4BwmlMpBRaZa0=
			</data>
		</dict>
		<key>presstube.scnassets/plus-04-26.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			jLhKuBE9JrgVf8fqdTcOrmr7h9E=
			</data>
			<key>hash2</key>
			<data>
			9O7fRcCihlegCiFtREbf7lSvcv1mjzTmTXUJtV40sEg=
			</data>
		</dict>
		<key>presstube.scnassets/sun-04-21-22-black.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			aR/D2u4k3BlINiLca3IU1PEKr4g=
			</data>
			<key>hash2</key>
			<data>
			b7zOUBo5mOwYuRvF9iQXVhoXdz18HmwLmZgoaZKv7w0=
			</data>
		</dict>
		<key>presstube.scnassets/sun-04-21-22.ktx</key>
		<dict>
			<key>hash</key>
			<data>
			lA7lTsFVGmP8aPsGLgJBgiRySH0=
			</data>
			<key>hash2</key>
			<data>
			JzsI63TTqvucOYBRKSgpVMHgd0OvnJxUmi4UDHos+fk=
			</data>
		</dict>
		<key>shape-tap-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			f7S6R5eI0Vn1lI3sM5OnvK9OXlU=
			</data>
			<key>hash2</key>
			<data>
			kCtVocjiStfLhZShGoQelFObsfJMyE1EtzOY5bELpqg=
			</data>
		</dict>
		<key>shape-tap-2.caf</key>
		<dict>
			<key>hash</key>
			<data>
			98Po0gqS1DYf8DQIOp8XKmjYzYU=
			</data>
			<key>hash2</key>
			<data>
			58AwsPli7Tn3pkiqq/PCfjspAsmRwgP5srPzAbC5/B8=
			</data>
		</dict>
		<key>shape-tap-3.caf</key>
		<dict>
			<key>hash</key>
			<data>
			kc7kqesyhi7gL8lt826TZUldgok=
			</data>
			<key>hash2</key>
			<data>
			6dvXYcT07xoN/l3p6Nb6dtU5lKlOlo3CpedkCR/R+9I=
			</data>
		</dict>
		<key>shape-tap-4.caf</key>
		<dict>
			<key>hash</key>
			<data>
			aTzqP/0MODGS8dLdCddJE96x2HI=
			</data>
			<key>hash2</key>
			<data>
			/Xx+Y4H1GlMUSSAln/EtflXnYPR1obrlgG5x8GKZI8k=
			</data>
		</dict>
		<key>shape-tap-5.caf</key>
		<dict>
			<key>hash</key>
			<data>
			/vlmI3zugS3yYeextsMNK5X7Aqc=
			</data>
			<key>hash2</key>
			<data>
			lne8+ib3B3NZuqcelcHqGW3orIZ6eOhOYYdl3iVhHjg=
			</data>
		</dict>
		<key>shape-tap-6.caf</key>
		<dict>
			<key>hash</key>
			<data>
			fOdpmimt4lkLb/R+VUd2Cp6WUQg=
			</data>
			<key>hash2</key>
			<data>
			ODuNc3NVRIWtAoj7oi3GR0NhxIQp6a1E04s/Nqxx8ZY=
			</data>
		</dict>
		<key>shape-tap-7.caf</key>
		<dict>
			<key>hash</key>
			<data>
			7Qjjk1HfsGq7GzVRABZR527NFGY=
			</data>
			<key>hash2</key>
			<data>
			juvvFf48o5m0gRYIWA2dsdC3VOBKnCS1P3x75J5tr0o=
			</data>
		</dict>
		<key>shape-tap-8.caf</key>
		<dict>
			<key>hash</key>
			<data>
			ufyRPHgpfnl1+4bQGOTlSTdU13s=
			</data>
			<key>hash2</key>
			<data>
			KRR6cT2Xd/qZyHtT6RL4rdhBMfWNCvScjsPtgNMefAg=
			</data>
		</dict>
		<key>shared.scnassets/Events.scn</key>
		<dict>
			<key>hash</key>
			<data>
			JVeX614gqeD7IKuQ00Z3bFmsLOY=
			</data>
			<key>hash2</key>
			<data>
			dPLOUj80x4zd4vmXSMYJ1Nh9hixhYljylvzFR1f5V/g=
			</data>
		</dict>
		<key>shirt003-texture-1024.heic</key>
		<dict>
			<key>hash</key>
			<data>
			CgK+4y13xrHpjJI8cc8aJ23dz3E=
			</data>
			<key>hash2</key>
			<data>
			yV8Y2es2rDENtW+zi+uxg5x1y0ERfG6Cp9lQHXMx3+I=
			</data>
		</dict>
		<key>shop.scnassets/shirt001-texture-1024.png</key>
		<dict>
			<key>hash</key>
			<data>
			AqIdM44/qLWuLbSmBbUDmWwx1EY=
			</data>
			<key>hash2</key>
			<data>
			vL3HxUQfCYrWAAkVP6U9X1QoWWfMepQ8N55IX21hYK8=
			</data>
		</dict>
		<key>shop.scnassets/shirt002-brown-1024.heic</key>
		<dict>
			<key>hash</key>
			<data>
			f8AWbWtqqa9z1DIhfkDlRLc3t48=
			</data>
			<key>hash2</key>
			<data>
			p/nTLwZn5mEy/4ISUTqugBhdXo984uG3BJDVEuThz+I=
			</data>
		</dict>
		<key>shop.scnassets/shop-items.scn</key>
		<dict>
			<key>hash</key>
			<data>
			lnljj3qhH74txLNLN/Hoj3f0BWE=
			</data>
			<key>hash2</key>
			<data>
			y1a9XmMKBr2FtezdMQFhW5HR20HTA0qFczow0dIfUWk=
			</data>
		</dict>
		<key>spin-fast-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			F7jJp9QnbsVBGoyJkLQdQG+MSDQ=
			</data>
			<key>hash2</key>
			<data>
			qTgcEU3aI71rs/Y+t+7k7xT4XrGoWZEHTZj37MWnm/0=
			</data>
		</dict>
		<key>spin-fast-2.caf</key>
		<dict>
			<key>hash</key>
			<data>
			CYpmFUhuc49R0OMqysYhNwFSNqY=
			</data>
			<key>hash2</key>
			<data>
			yLRXS2zAoNngmo3LOyyRyaan2xWxGcRAIEVuNqXKptY=
			</data>
		</dict>
		<key>spin-fast-3.caf</key>
		<dict>
			<key>hash</key>
			<data>
			78XRcaa7hEdvzYNRp439dTtt/Y4=
			</data>
			<key>hash2</key>
			<data>
			n6qXcuz7fnsZB1Mxj1z4Aby3P67+ZmQJ/IOT9rX0Q7Y=
			</data>
		</dict>
		<key>spin-fast-4.caf</key>
		<dict>
			<key>hash</key>
			<data>
			DfzsOUSsQaze1vO9UEApWHsPykU=
			</data>
			<key>hash2</key>
			<data>
			+02IrR+iqp7ObJlgoAQjOITIzTquvaMIX7ZzvLW/ids=
			</data>
		</dict>
		<key>spin-fast-5.caf</key>
		<dict>
			<key>hash</key>
			<data>
			HLZvcnmxLqggzr/VGzx5GUNKgKc=
			</data>
			<key>hash2</key>
			<data>
			GyVN/w9TL5WVX8jPFc/13aqujY1eevi0fBTOs6B7dVU=
			</data>
		</dict>
		<key>spin-fast-6.caf</key>
		<dict>
			<key>hash</key>
			<data>
			d04lnt0T31dMbsKR6DY1YbWTLUM=
			</data>
			<key>hash2</key>
			<data>
			sCxSl/zipu6J/7m7uVVHMDixo3FpXlI7AcrQ3kFuNAQ=
			</data>
		</dict>
		<key>spin-slow-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			cd4Wk4ATor1Ej4RaOzodGy1y75A=
			</data>
			<key>hash2</key>
			<data>
			2aiT3VNVG49xUb7VPACzfAwsLISbTOCyIm2IjNX6ET0=
			</data>
		</dict>
		<key>spin-slow-10.caf</key>
		<dict>
			<key>hash</key>
			<data>
			MqO7l8o/iDehwpOlqTnyRHL2mL0=
			</data>
			<key>hash2</key>
			<data>
			5DWVLBRPJsgM6E88grwdB1ts5qx+nPHyq4Ilc0J+L1E=
			</data>
		</dict>
		<key>spin-slow-2.caf</key>
		<dict>
			<key>hash</key>
			<data>
			1hX1O21bFkxBwr+TH0zHUSWXXyk=
			</data>
			<key>hash2</key>
			<data>
			WTvZDN20a/hWW6GODBj5k7bZnV+ZYeD32+dErK8B/zQ=
			</data>
		</dict>
		<key>spin-slow-3.caf</key>
		<dict>
			<key>hash</key>
			<data>
			qNbQprkWcYlBDi38cd4OqvC/5g4=
			</data>
			<key>hash2</key>
			<data>
			fdQUQ0pJGA5digp5T8OoQmCzRSPsxqBchSelRNH2kXU=
			</data>
		</dict>
		<key>spin-slow-4.caf</key>
		<dict>
			<key>hash</key>
			<data>
			/seiNeXrv/vDMQtN3rot+PDSJJg=
			</data>
			<key>hash2</key>
			<data>
			y+KbXIg7ax8s0oukxRC86pIGpLbBgCPAfSVXfh+jMwA=
			</data>
		</dict>
		<key>spin-slow-5.caf</key>
		<dict>
			<key>hash</key>
			<data>
			6/Aoae4dMg1fW4L1Xqmj5+5ZegE=
			</data>
			<key>hash2</key>
			<data>
			/7VzWGfbWn3OQHLRRDFQbXaFJZO1178Ipjlt/C8Cjhc=
			</data>
		</dict>
		<key>spin-slow-6.caf</key>
		<dict>
			<key>hash</key>
			<data>
			YV+zn8GQtpSoMOmAQD5N3/JFALM=
			</data>
			<key>hash2</key>
			<data>
			o78Mq5hSBqKwkaMR2uzaYcTEfA2TQzmPKqst2JlhvsQ=
			</data>
		</dict>
		<key>spin-slow-7.caf</key>
		<dict>
			<key>hash</key>
			<data>
			KWJgYDz4Zavr4bejryEN/DMpvGg=
			</data>
			<key>hash2</key>
			<data>
			1NeACdduzOomPwJsGcz9u63uynpeXtQ6CY7LPGfhxHQ=
			</data>
		</dict>
		<key>spin-slow-8.caf</key>
		<dict>
			<key>hash</key>
			<data>
			B7sPvcKHFoRh2fdBsPbKjA8TJyQ=
			</data>
			<key>hash2</key>
			<data>
			BWvYzzOX28LKqSVDp4U7WtMa8UOt/pX2CTS6xuqwGnQ=
			</data>
		</dict>
		<key>spin-slow-9.caf</key>
		<dict>
			<key>hash</key>
			<data>
			lGdpQWZlmP3gv/Ggh/1e0t5wr9k=
			</data>
			<key>hash2</key>
			<data>
			dl+nL1ed3aNcLGISCt87LqBIEbx63j9gE7U6eUJhY+o=
			</data>
		</dict>
		<key>sun-tapped-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			ODMbj6C1YzyD7f+D3KbTyvaGLUc=
			</data>
			<key>hash2</key>
			<data>
			a/vlhBHfIsfzGs6aVyvk8nFr1b5yumzqR8cInm5beZc=
			</data>
		</dict>
		<key>sun-tapped-2.caf</key>
		<dict>
			<key>hash</key>
			<data>
			+SJkLEhYE3TuCemb5sVuCSsNG+4=
			</data>
			<key>hash2</key>
			<data>
			0F5w0HvpkcR+fzWbPAcOft67q+kWGJEx9fMGVEQbBOw=
			</data>
		</dict>
		<key>sun-tapped-3.caf</key>
		<dict>
			<key>hash</key>
			<data>
			EtVUToUwJ1Bhuh60s303D7s4wWc=
			</data>
			<key>hash2</key>
			<data>
			k5DAfwaA5v113U1ZSBpMgBmDTqqc41CvfqoZiFA89sU=
			</data>
		</dict>
		<key>sun-tapped-4.caf</key>
		<dict>
			<key>hash</key>
			<data>
			moECo92NP/6L52pAGj3eeVZu1Ug=
			</data>
			<key>hash2</key>
			<data>
			GjtycFjMRwR29atTWzXaBzKg7LmOA6OeHRlPqEl0LEI=
			</data>
		</dict>
		<key>sun-tapped-5.caf</key>
		<dict>
			<key>hash</key>
			<data>
			j+BehnQsxIzo5W/4hcpCukI1jV8=
			</data>
			<key>hash2</key>
			<data>
			XCOv/AVja/cHEdxZ5YVor9Tvz9inI1XaiD4B0la7bNg=
			</data>
		</dict>
		<key>sun-tapped-6.caf</key>
		<dict>
			<key>hash</key>
			<data>
			w4pi1fFgHlGBpQyBVuuPi1ZaWhY=
			</data>
			<key>hash2</key>
			<data>
			3b6J45VPM7pNAXwb57Ny/8OvZqbba6ogGGMuJB1S6ME=
			</data>
		</dict>
		<key>timer-pass-center-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			m8bLn49O/FEOgtln6zGCZKV4Bgo=
			</data>
			<key>hash2</key>
			<data>
			la7bYsAC9tPFuR49RxGUt+Irn1qkX8qw6f/0XXBX5YU=
			</data>
		</dict>
		<key>ui-day-select-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			jFNNao//I+ldCTBIHi3a439btK0=
			</data>
			<key>hash2</key>
			<data>
			OBMT69PIiU0PKsbZD6dRkiUBDow2FEUe+aN0VixT1Sk=
			</data>
		</dict>
		<key>ui-day-select-2.caf</key>
		<dict>
			<key>hash</key>
			<data>
			LMQnOUoAglwqXpMJf3M4nt+rd5A=
			</data>
			<key>hash2</key>
			<data>
			1pPuaCsj1JySL4HqOXWGV40/qkCNhYvT+04KYT/NSMw=
			</data>
		</dict>
		<key>ui-day-select-3.caf</key>
		<dict>
			<key>hash</key>
			<data>
			FnZRH4FfWTLDA7V5d1ppd9e8gAE=
			</data>
			<key>hash2</key>
			<data>
			Gv9BEfT83m50iEikY2e6UbPn+J+vdfi/sp6MXVA1kkE=
			</data>
		</dict>
		<key>ui-day-select-4.caf</key>
		<dict>
			<key>hash</key>
			<data>
			Xy2CaA74prt/88AenGQB2WKdvL8=
			</data>
			<key>hash2</key>
			<data>
			K2esw5Yr1R4fnCwh4Z6GuwFdRbVvnwQU1iiNS/EDG0w=
			</data>
		</dict>
		<key>ui-day-select-5.caf</key>
		<dict>
			<key>hash</key>
			<data>
			QE06/BTC/0fYFO8d/qIMm5xmLF4=
			</data>
			<key>hash2</key>
			<data>
			bnJP2tQ+w4QkvNVsX4gvziEImFDG4lwzlrPl+tI/m5A=
			</data>
		</dict>
		<key>ui-day-select-6.caf</key>
		<dict>
			<key>hash</key>
			<data>
			3HMAqNgA9F5e3+uYCYpFfT1mMpA=
			</data>
			<key>hash2</key>
			<data>
			ZBWAyO5AdciQ8A9pVvUjRvRvZN+0I6A6C9rXj9mLDUI=
			</data>
		</dict>
		<key>ui-day-select-7.caf</key>
		<dict>
			<key>hash</key>
			<data>
			qx217DcjNd6yFrSlr59Qhvsmla8=
			</data>
			<key>hash2</key>
			<data>
			ekxA74Y/WhPnDb2+lyHaQ86GKKs1OUbn3rgjLWPYDyU=
			</data>
		</dict>
		<key>ui-menu-swipe-down-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			VUacGItmK0laQTodgXVmKsW6FZQ=
			</data>
			<key>hash2</key>
			<data>
			+3owG6FtGH49jdwDfi/gXkXCw6gG96TqzQx/Gn0j098=
			</data>
		</dict>
		<key>ui-menu-swipe-up-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			i1Ugl+KoNyMSFIHyBm9iSPcRYg8=
			</data>
			<key>hash2</key>
			<data>
			vCQi4RY5qshrLuJEeoNziNq8K5VqJWDmwC8OJ4QVH0g=
			</data>
		</dict>
		<key>ui-time-scroll-click-1.caf</key>
		<dict>
			<key>hash</key>
			<data>
			3xPkKGFmDJa56XDnrEeFx+/7M0o=
			</data>
			<key>hash2</key>
			<data>
			MzPlKpjhtmRqDxNhtXDJEOZnGm+ILfyUhunf63n7EVQ=
			</data>
		</dict>
		<key>ui-time-scroll-click-2.caf</key>
		<dict>
			<key>hash</key>
			<data>
			X/gimLmmj/xQ6RLGZANqJn5mFwk=
			</data>
			<key>hash2</key>
			<data>
			7fPE/fUSiOGJrJDbNbxMzPddfCSOgumM8WOdubyCCqI=
			</data>
		</dict>
		<key>ui-time-scroll-click-3.caf</key>
		<dict>
			<key>hash</key>
			<data>
			JOoPNDx4Bh+O7OOBdTSwxnpVpG8=
			</data>
			<key>hash2</key>
			<data>
			rzcDwIs/R3AmNm/P0lwnpThpJ4t2LLUcrd90MwBK2cc=
			</data>
		</dict>
		<key>ui-time-scroll-click-4.caf</key>
		<dict>
			<key>hash</key>
			<data>
			M3q/QP1B8W89F1QXWdu5oNYW5Vo=
			</data>
			<key>hash2</key>
			<data>
			kX/Om/tBuBOXrTjJs+gAoA1DY9mEHkH5vas1akOFlnc=
			</data>
		</dict>
		<key>ui-time-scroll-click-5.caf</key>
		<dict>
			<key>hash</key>
			<data>
			2Wc3CHhMz8Hr29x+kWel/EE0Vmc=
			</data>
			<key>hash2</key>
			<data>
			IuYbL8Nf2VNnXfQGn4R2mTnK0OztSVFBZhpEcCY7YJ4=
			</data>
		</dict>
		<key>ui-time-scroll-click-6.caf</key>
		<dict>
			<key>hash</key>
			<data>
			K+230SQnQnc0q2SYNlf+xVeNtAI=
			</data>
			<key>hash2</key>
			<data>
			V25h7JEB+HcIoe41RwV0u/8fQp3gTB0ddvj0kZiFp98=
			</data>
		</dict>
		<key>weather-ambience-clear.caf</key>
		<dict>
			<key>hash</key>
			<data>
			rTkE/vQjLJkViPu2xtTAi1wcHDw=
			</data>
			<key>hash2</key>
			<data>
			c+S+KM/GESQuw06zwERgSeIaDWSNznq1K0dyCAa+TFw=
			</data>
		</dict>
		<key>weather-ambience-cloudy.caf</key>
		<dict>
			<key>hash</key>
			<data>
			fapnIRc6vToyMiw5gI3fU1MqJnA=
			</data>
			<key>hash2</key>
			<data>
			3bhVLMBx5/mEtki8qTpk8JZLUBF+IGFnl8pXNKAopbA=
			</data>
		</dict>
		<key>weather-ambience-icepellets.caf</key>
		<dict>
			<key>hash</key>
			<data>
			6UQi+UGACqe/f6SHtD8ifClctcQ=
			</data>
			<key>hash2</key>
			<data>
			LfmYszmHDuTsNh1K5wjRCfIfILI588YvWArraOmNTM4=
			</data>
		</dict>
		<key>weather-ambience-mostlyClear.caf</key>
		<dict>
			<key>hash</key>
			<data>
			KAXf3Ojgtwx5lX50mB6mfFfGcJ0=
			</data>
			<key>hash2</key>
			<data>
			yBqK7eZzSwdhct33vI5NC3szCVMIbsUhwMOFb2m0WHA=
			</data>
		</dict>
		<key>weather-ambience-rain.caf</key>
		<dict>
			<key>hash</key>
			<data>
			GpZjMDKzuJXVcGbm7EO9QgaclHI=
			</data>
			<key>hash2</key>
			<data>
			xjv2GtNAUDp6Mwgn7mB6RnxKk55w8fQGnIUOFGjPyDs=
			</data>
		</dict>
		<key>weather-ambience-rainLight.caf</key>
		<dict>
			<key>hash</key>
			<data>
			4FRJSNsf5uoS+ioqbvXstJizssA=
			</data>
			<key>hash2</key>
			<data>
			grS0MbLuRMxTJMGZWC7j5Mj9amprg4lTMspNFuvv2Z8=
			</data>
		</dict>
		<key>weather-ambience-snow.caf</key>
		<dict>
			<key>hash</key>
			<data>
			869B40fasl5ehl1TGRFvcTB/Ojs=
			</data>
			<key>hash2</key>
			<data>
			ANaSQG442R7EGYYMIA6VH/lbLhRiCfaDzPxfTyJG8Dk=
			</data>
		</dict>
		<key>weather-ambience-thunderstorm.caf</key>
		<dict>
			<key>hash</key>
			<data>
			7V29LjXV38k5AjqWZ0pU40Szo/Y=
			</data>
			<key>hash2</key>
			<data>
			A//2uTxdSgEozLYGpG47rnju6XOq/1D1UMYfoA/kSkY=
			</data>
		</dict>
		<key>weather-sting-clear.caf</key>
		<dict>
			<key>hash</key>
			<data>
			dbNSl2qhtMSs05yj0oavjQZDv9M=
			</data>
			<key>hash2</key>
			<data>
			ele3z8vsTwnxVWy9Vnt8MyPUur1apIy0/SI2e3dW5iU=
			</data>
		</dict>
		<key>weather-sting-cloudy.caf</key>
		<dict>
			<key>hash</key>
			<data>
			OyYx9Qu20QmERHv7FWlSI1YAAAQ=
			</data>
			<key>hash2</key>
			<data>
			aNszOBrRPHsoaxd8FZolvWBCPyuK0DyPZb1olIEklEs=
			</data>
		</dict>
		<key>weather-sting-fog.caf</key>
		<dict>
			<key>hash</key>
			<data>
			Ktmfxx7x506mJvejBnnG4S8UNZg=
			</data>
			<key>hash2</key>
			<data>
			85bduLVEHI5zL/Y3CNo4n+J8SqTv8yyofM7cTvAsccI=
			</data>
		</dict>
		<key>weather-sting-heavysnow.caf</key>
		<dict>
			<key>hash</key>
			<data>
			vmmthv1lIKlnDTT9uEQmL6lgmkw=
			</data>
			<key>hash2</key>
			<data>
			RPsuhVuvVGHW15W7dmzfLlk7765AaCX4iLEr4Rm5TY8=
			</data>
		</dict>
		<key>weather-sting-icepellets.caf</key>
		<dict>
			<key>hash</key>
			<data>
			2s5pc98i5d8VcAy+NtCkx8XxAHo=
			</data>
			<key>hash2</key>
			<data>
			/UYLjdeu4MMxZ4R4QeStl8SFec5o4ZGyu9yUpoZjy/w=
			</data>
		</dict>
		<key>weather-sting-lightrain.caf</key>
		<dict>
			<key>hash</key>
			<data>
			XcvG1oHs806kHsR2/oIoazcF/bo=
			</data>
			<key>hash2</key>
			<data>
			xsyxKv3GCqDj76yXDZ1l8g6oxP45LMX19qyKrPYXXR0=
			</data>
		</dict>
		<key>weather-sting-mostlyclear.caf</key>
		<dict>
			<key>hash</key>
			<data>
			kjHCdM2GHnKr/2lMyN9kvVYuVuU=
			</data>
			<key>hash2</key>
			<data>
			WVZ+OTKhRHHKx0+XaT7lClCnGG3gsmei7ZKd7hONs3s=
			</data>
		</dict>
		<key>weather-sting-mostlycloudy.caf</key>
		<dict>
			<key>hash</key>
			<data>
			OGX3gNfAgZyRrDh3orDkNzohWVI=
			</data>
			<key>hash2</key>
			<data>
			5bSzTx6NYIQJa+JOL5xboQLmWvcn7Lsx2ia6ax/JRCs=
			</data>
		</dict>
		<key>weather-sting-partlycloudy.caf</key>
		<dict>
			<key>hash</key>
			<data>
			Wmw44szl9ZbWomsprpc+vpzQggQ=
			</data>
			<key>hash2</key>
			<data>
			Vuhk1UNqG/7pXQwRIGDVGWxKZQCpZ/02gl62hveY/b0=
			</data>
		</dict>
		<key>weather-sting-rain.caf</key>
		<dict>
			<key>hash</key>
			<data>
			LlX7p6DBDz4UTiG0kG46A64BE04=
			</data>
			<key>hash2</key>
			<data>
			d+o9bnS8vby8ToKElB9/JJYoqo94AGUABZ8VgpP1uws=
			</data>
		</dict>
		<key>weather-sting-smoke.caf</key>
		<dict>
			<key>hash</key>
			<data>
			Cj+WmuLLQmz9ox74rWrRYhX6DHI=
			</data>
			<key>hash2</key>
			<data>
			wdroy3zftInKUwMOxam5SNdElFXM3RtHhE9T2AV5z64=
			</data>
		</dict>
		<key>weather-sting-snow.caf</key>
		<dict>
			<key>hash</key>
			<data>
			Fan8SALXHFuU7DfRQE3/ky4CUjc=
			</data>
			<key>hash2</key>
			<data>
			bqYpiTxLRA5PFxC9OUD9DWPhMt0t3IQSutviN9o55Dw=
			</data>
		</dict>
		<key>weather-sting-thunder.caf</key>
		<dict>
			<key>hash</key>
			<data>
			VjwW3WNEuBr2B5JdLsFp6FLOyRg=
			</data>
			<key>hash2</key>
			<data>
			bRCirv43k2UknV3qpEOlvQWSD3NNMtjnKtRvwS5hxss=
			</data>
		</dict>
		<key>wireframe-animation.json</key>
		<dict>
			<key>hash</key>
			<data>
			oPf7NGvKfp9A/y1DmScXTxiRbzs=
			</data>
			<key>hash2</key>
			<data>
			NJzVFC6uZG5N9/qy+4UzDWt1zzvZiqw1ecPw0Fgh9vs=
			</data>
		</dict>
		<key>wireframe-colors.json</key>
		<dict>
			<key>hash</key>
			<data>
			SkET4REpucuQlvEopWhZgzy3xvY=
			</data>
			<key>hash2</key>
			<data>
			7DX6SYJJ8oNb30ouU3mQFIaVOXHpJr2EDpTKKerWq48=
			</data>
		</dict>
		<key>wireframe-theme.json</key>
		<dict>
			<key>hash</key>
			<data>
			8ji1OdI/yl3KdcG520CtnE7BUCE=
			</data>
			<key>hash2</key>
			<data>
			jZsIJ5Tbo5Mjwkqj+G2GWH5sLwTbFt90hIEDrk5l1O0=
			</data>
		</dict>
		<key>wireframe.scnassets/Main.scn</key>
		<dict>
			<key>hash</key>
			<data>
			oYmB5lS518lfCPNkPbWPNn2/p1s=
			</data>
			<key>hash2</key>
			<data>
			ZH2cIs5T71Lm3zP4wnFYYB1IPputCMpcMYrx2AeCOgU=
			</data>
		</dict>
		<key>wireframe.scnassets/Models.scn</key>
		<dict>
			<key>hash</key>
			<data>
			Hm1J65Bd9O3AZz9KwKro0eUmDIw=
			</data>
			<key>hash2</key>
			<data>
			e7adw6MOFXitXRzMbxJCXNNAk03WBp4juthPsav+eoQ=
			</data>
		</dict>
		<key>wireframe.scnassets/Numbers.scn</key>
		<dict>
			<key>hash</key>
			<data>
			0VnOYMxxDfPcDVqjtdVp6jZ+0Vc=
			</data>
			<key>hash2</key>
			<data>
			AQYjPKevPi30j/abN0apboVVsaBgf6T3CA8IBV0Lhvc=
			</data>
		</dict>
		<key>wireframe.scnassets/Particles.scn</key>
		<dict>
			<key>hash</key>
			<data>
			Cc2hQ+T32TiL4PiDCYo5wTm7d5Q=
			</data>
			<key>hash2</key>
			<data>
			YSUNmBoRQiIKHlhKu/+PCHouPoLLsszPzYC3PYdewI8=
			</data>
		</dict>
		<key>zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9LIKlkeOyWKJqQFP4AIjSKjLKwQ=
			</data>
			<key>hash2</key>
			<data>
			iESK6/CswfCN3fA9Q8vUhOW5JpJueiBn3UCgz0wSzNw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Shi/GjyOXaU+CsI6mdntDrdGAW0=
			</data>
			<key>hash2</key>
			<data>
			WYIHFBaN+pGrE3tpPWaqlGI4FPDPF0IZigbiVXbRHrM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hant.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/7Nwpqze+YSTj5+sUU+3HX8/6+k=
			</data>
			<key>hash2</key>
			<data>
			RnYCm+E7tt0ZsYTy/bsLz4G5kGSjDM4O9zuX4yIoTWE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hant.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+ciEm+qDGp+UZoYEmWF/ETcdQhA=
			</data>
			<key>hash2</key>
			<data>
			XjAQ/6tUd/9Oo4HuqlW1gDE2aVZnc+4gveFjkxo3G24=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>PlugIns/widget-mainExtension\.appex/SC_Info/widget-mainExtension\.(sinf|supp|supf|supx)$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<integer>10000</integer>
		</dict>
		<key>SC_Info/simple-weather\.(sinf|supp|supf|supx)$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<integer>10000</integer>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>PlugIns/widget-mainExtension\.appex/SC_Info/widget-mainExtension\.(sinf|supp|supf|supx)$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<integer>10000</integer>
		</dict>
		<key>SC_Info/simple-weather\.(sinf|supp|supf|supx)$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<integer>10000</integer>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
