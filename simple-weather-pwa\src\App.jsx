import React, { useState, useEffect } from 'react'
import { Canvas } from '@react-three/fiber'
import WeatherScene from './components/WeatherScene'
import WeatherUI from './components/WeatherUI'
import ThemeSelector from './components/ThemeSelector'
import LoadingScreen from './components/LoadingScreen'
import InstallPrompt from './components/InstallPrompt'
import { useWeatherData } from './hooks/useWeatherData'
import { useGeolocation } from './hooks/useGeolocation'
import { usePWA } from './hooks/usePWA'
import { weatherThemes } from './data/themes'
import './styles/App.css'

function App() {
  const [currentTheme, setCurrentTheme] = useState('normal')
  const [isLoading, setIsLoading] = useState(true)
  const [showThemeSelector, setShowThemeSelector] = useState(false)
  const [showInstallPrompt, setShowInstallPrompt] = useState(false)

  const { location, error: locationError } = useGeolocation()
  const { weatherData, loading: weatherLoading, error: weatherError } = useWeatherData(location)
  const { isInstallable, isInstalled, installApp } = usePWA()

  useEffect(() => {
    // Hide loading screen after initial data loads
    if (!weatherLoading && weatherData) {
      setTimeout(() => setIsLoading(false), 1000)
    }
  }, [weatherLoading, weatherData])

  // Show install prompt after app loads (if installable and not installed)
  useEffect(() => {
    if (!isLoading && isInstallable && !isInstalled) {
      const timer = setTimeout(() => {
        setShowInstallPrompt(true)
      }, 3000) // Show after 3 seconds

      return () => clearTimeout(timer)
    }
  }, [isLoading, isInstallable, isInstalled])

  const handleThemeChange = (themeId) => {
    setCurrentTheme(themeId)
    setShowThemeSelector(false)
  }

  const handleInstallApp = async () => {
    const success = await installApp()
    if (success) {
      setShowInstallPrompt(false)
    }
  }

  const handleDismissInstall = () => {
    setShowInstallPrompt(false)
    // Don't show again for this session
    sessionStorage.setItem('installPromptDismissed', 'true')
  }

  const theme = weatherThemes[currentTheme] || weatherThemes.normal

  if (isLoading) {
    return <LoadingScreen />
  }

  return (
    <div className="app" style={{ background: theme.colors.background }}>
      {/* 3D Weather Scene */}
      <Canvas
        camera={{ position: [0, 0, 5], fov: 75 }}
        style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
      >
        <WeatherScene 
          weatherData={weatherData}
          theme={theme}
        />
      </Canvas>

      {/* Weather UI Overlay */}
      <WeatherUI 
        weatherData={weatherData}
        theme={theme}
        onThemeToggle={() => setShowThemeSelector(!showThemeSelector)}
        locationError={locationError}
        weatherError={weatherError}
      />

      {/* Theme Selector Modal */}
      {showThemeSelector && (
        <ThemeSelector
          themes={weatherThemes}
          currentTheme={currentTheme}
          onThemeSelect={handleThemeChange}
          onClose={() => setShowThemeSelector(false)}
        />
      )}

      {/* Install Prompt */}
      {showInstallPrompt && (
        <InstallPrompt
          onInstall={handleInstallApp}
          onDismiss={handleDismissInstall}
        />
      )}
    </div>
  )
}

export default App
