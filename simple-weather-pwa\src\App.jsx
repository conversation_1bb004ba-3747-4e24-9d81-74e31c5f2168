import React, { useState, useEffect } from 'react'
import { Canvas } from '@react-three/fiber'
import WeatherScene from './components/WeatherScene'
import WeatherUI from './components/WeatherUI'
import ThemeSelector from './components/ThemeSelector'
import LoadingScreen from './components/LoadingScreen'
import { useWeatherData } from './hooks/useWeatherData'
import { useGeolocation } from './hooks/useGeolocation'
import { weatherThemes } from './data/themes'
import './styles/App.css'

function App() {
  const [currentTheme, setCurrentTheme] = useState('normal')
  const [isLoading, setIsLoading] = useState(true)
  const [showThemeSelector, setShowThemeSelector] = useState(false)
  
  const { location, error: locationError } = useGeolocation()
  const { weatherData, loading: weatherLoading, error: weatherError } = useWeatherData(location)

  useEffect(() => {
    // Hide loading screen after initial data loads
    if (!weatherLoading && weatherData) {
      setTimeout(() => setIsLoading(false), 1000)
    }
  }, [weatherLoading, weatherData])

  const handleThemeChange = (themeId) => {
    setCurrentTheme(themeId)
    setShowThemeSelector(false)
  }

  const theme = weatherThemes[currentTheme] || weatherThemes.normal

  if (isLoading) {
    return <LoadingScreen />
  }

  return (
    <div className="app" style={{ background: theme.colors.background }}>
      {/* 3D Weather Scene */}
      <Canvas
        camera={{ position: [0, 0, 5], fov: 75 }}
        style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
      >
        <WeatherScene 
          weatherData={weatherData}
          theme={theme}
        />
      </Canvas>

      {/* Weather UI Overlay */}
      <WeatherUI 
        weatherData={weatherData}
        theme={theme}
        onThemeToggle={() => setShowThemeSelector(!showThemeSelector)}
        locationError={locationError}
        weatherError={weatherError}
      />

      {/* Theme Selector Modal */}
      {showThemeSelector && (
        <ThemeSelector
          themes={weatherThemes}
          currentTheme={currentTheme}
          onThemeSelect={handleThemeChange}
          onClose={() => setShowThemeSelector(false)}
        />
      )}
    </div>
  )
}

export default App
