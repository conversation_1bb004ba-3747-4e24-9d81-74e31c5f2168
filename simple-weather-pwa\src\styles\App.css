.app {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  transition: background 0.5s ease;
}

/* Weather UI Overlay */
.weather-ui {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.weather-ui > * {
  pointer-events: auto;
}

/* Header */
.weather-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  z-index: 20;
}

.location-info {
  color: white;
  text-align: left;
}

.location-name {
  font-size: 18px;
  font-weight: 300;
  margin-bottom: 4px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.current-time {
  font-size: 14px;
  opacity: 0.8;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.theme-toggle {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* Main Weather Display */
.main-weather {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  z-index: 15;
}

.temperature {
  font-size: 72px;
  font-weight: 100;
  margin-bottom: 8px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  line-height: 1;
}

.weather-condition {
  font-size: 20px;
  font-weight: 300;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  text-transform: capitalize;
}

.feels-like {
  font-size: 16px;
  opacity: 0.8;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Bottom Panel */
.bottom-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  padding: 20px;
  z-index: 20;
}

.weather-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.detail-item {
  text-align: center;
  color: white;
}

.detail-label {
  font-size: 12px;
  opacity: 0.7;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
}

/* Hourly Forecast */
.hourly-forecast {
  overflow-x: auto;
  padding-bottom: 10px;
}

.hourly-scroll {
  display: flex;
  gap: 16px;
  padding: 0 4px;
}

.hourly-item {
  flex-shrink: 0;
  text-align: center;
  color: white;
  min-width: 60px;
}

.hourly-time {
  font-size: 12px;
  opacity: 0.7;
  margin-bottom: 8px;
}

.hourly-icon {
  width: 24px;
  height: 24px;
  margin: 0 auto 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.hourly-temp {
  font-size: 14px;
  font-weight: 500;
}

/* Error States */
.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  background: rgba(255, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 0, 0, 0.3);
  border-radius: 12px;
  padding: 20px;
  max-width: 300px;
}

.error-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
}

.error-description {
  font-size: 14px;
  opacity: 0.8;
  line-height: 1.4;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .weather-header {
    padding: 16px;
  }
  
  .temperature {
    font-size: 56px;
  }
  
  .weather-condition {
    font-size: 18px;
  }
  
  .bottom-panel {
    padding: 16px;
  }
  
  .weather-details {
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
  }
  
  .detail-value {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .temperature {
    font-size: 48px;
  }
  
  .weather-details {
    grid-template-columns: repeat(3, 1fr);
  }
}
