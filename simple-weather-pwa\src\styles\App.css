.app {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  transition: background 0.5s ease;
}

/* Weather UI Overlay */
.weather-ui {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.weather-ui > * {
  pointer-events: auto;
}

/* Header */
.weather-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 24px 24px 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  z-index: 20;
  padding-top: env(safe-area-inset-top, 24px);
}

.location-info {
  color: white;
  text-align: left;
  flex: 1;
}

.location-name {
  font-size: 20px;
  font-weight: 400;
  margin-bottom: 6px;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
  letter-spacing: 0.2px;
}

.current-time {
  font-size: 16px;
  opacity: 0.85;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 300;
  letter-spacing: 0.3px;
}

.theme-toggle {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 14px;
  color: white;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px;
  min-height: 48px;
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.08);
  border-color: rgba(255, 255, 255, 0.25);
}

.theme-toggle:active {
  transform: scale(0.95);
}

/* Main Weather Display */
.main-weather {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  z-index: 15;
  user-select: none;
}

.temperature {
  font-size: 120px;
  font-weight: 100;
  margin-bottom: 12px;
  text-shadow:
    0 8px 16px rgba(0, 0, 0, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2);
  line-height: 0.9;
  letter-spacing: -0.02em;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
  position: relative;
}

.temperature::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(0, 0, 0, 0.1) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  pointer-events: none;
}

.weather-condition {
  font-size: 24px;
  font-weight: 300;
  margin-bottom: 20px;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
  text-transform: capitalize;
  letter-spacing: 0.5px;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
}

.feels-like {
  font-size: 18px;
  opacity: 0.85;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 300;
  letter-spacing: 0.3px;
}

/* Bottom Panel */
.bottom-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  padding: 24px 24px calc(24px + env(safe-area-inset-bottom, 0px));
  z-index: 20;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.weather-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 28px;
  padding: 0 4px;
}

.detail-item {
  text-align: center;
  color: white;
  padding: 8px 4px;
}

.detail-label {
  font-size: 13px;
  opacity: 0.75;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  font-weight: 400;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
}

.detail-value {
  font-size: 18px;
  font-weight: 300;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
}

/* Hourly Forecast */
.hourly-forecast {
  overflow-x: auto;
  padding-bottom: 12px;
  -webkit-overflow-scrolling: touch;
}

.hourly-forecast::-webkit-scrollbar {
  display: none;
}

.hourly-scroll {
  display: flex;
  gap: 20px;
  padding: 0 8px;
  min-width: max-content;
}

.hourly-item {
  flex-shrink: 0;
  text-align: center;
  color: white;
  min-width: 64px;
  padding: 8px 4px;
}

.hourly-time {
  font-size: 13px;
  opacity: 0.8;
  margin-bottom: 10px;
  font-weight: 400;
  letter-spacing: 0.2px;
}

.hourly-icon {
  width: 28px;
  height: 28px;
  margin: 0 auto 10px;
  background: rgba(255, 255, 255, 0.12);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.hourly-temp {
  font-size: 16px;
  font-weight: 300;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
}

/* Error States */
.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  background: rgba(255, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 0, 0, 0.3);
  border-radius: 12px;
  padding: 20px;
  max-width: 300px;
}

.error-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
}

.error-description {
  font-size: 14px;
  opacity: 0.8;
  line-height: 1.4;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .weather-header {
    padding: 20px 20px 0 20px;
  }

  .temperature {
    font-size: 96px;
  }

  .weather-condition {
    font-size: 20px;
  }

  .bottom-panel {
    padding: 20px 20px calc(20px + env(safe-area-inset-bottom, 0px));
  }

  .weather-details {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }

  .detail-value {
    font-size: 16px;
  }

  .hourly-scroll {
    gap: 16px;
  }

  .hourly-item {
    min-width: 56px;
  }
}

@media (max-width: 480px) {
  .weather-header {
    padding: 16px 16px 0 16px;
  }

  .temperature {
    font-size: 80px;
  }

  .weather-condition {
    font-size: 18px;
  }

  .feels-like {
    font-size: 16px;
  }

  .bottom-panel {
    padding: 16px 16px calc(16px + env(safe-area-inset-bottom, 0px));
  }

  .weather-details {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .detail-value {
    font-size: 15px;
  }

  .detail-label {
    font-size: 12px;
  }

  .hourly-scroll {
    gap: 12px;
  }

  .hourly-item {
    min-width: 52px;
  }

  .hourly-temp {
    font-size: 15px;
  }
}

/* iOS-like safe area handling */
@supports (padding: max(0px)) {
  .weather-header {
    padding-top: max(24px, env(safe-area-inset-top));
  }

  .bottom-panel {
    padding-bottom: max(24px, env(safe-area-inset-bottom));
  }
}

/* High refresh rate displays */
@media (min-resolution: 120dpi) {
  .temperature {
    text-rendering: optimizeLegibility;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .glass {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.1);
  }
}
