{"animations": {"intro": {"actions": [{"nativeAnimation": "introSpin"}]}, "firstRunSpinInfinite": {"actions": [{"nativeAnimation": "firstRunSpinInfinite"}]}, "firstRunComplete": {"actions": [{"nativeAnimation": "firstRunComplete"}]}, "boltIn": {"actions": [{"animationNode": "bolt1", "targetNode": "^bolt-1-", "removedOnCompletion": false, "repeatCount": -1, "fillsForward": true}, {"animationNode": "bolt2", "targetNode": "^bolt-2-", "removedOnCompletion": false, "repeatCount": -1, "fillsForward": true}, {"animationNode": "bolt3", "targetNode": "^bolt-3-", "removedOnCompletion": false, "repeatCount": -1, "fillsForward": true}]}, "boltOut": {"actions": [{"nativeAnimation": "removeAllAnimations", "targetNode": "^bolt-1-"}, {"nativeAnimation": "removeAllAnimations", "targetNode": "^bolt-2-"}, {"nativeAnimation": "removeAllAnimations", "targetNode": "^bolt-3-"}]}, "sunIn": {"actions": [{"nativeAnimation": "planeColor", "targetColor": "255 255 255"}, {"nativeAnimation": "planeNoiseIntensity", "intensity": 0.4}, {"nativeAnimation": "fogColor", "targetColor": "250 250 250 245"}, {"nativeAnimation": "statusBarStyle", "statusBarStyle": "dark"}]}, "moonIn": {"actions": [{"nativeAnimation": "planeColor", "targetColor": "30 30 30"}, {"nativeAnimation": "planeNoiseIntensity", "intensity": 0.5}, {"nativeAnimation": "fogColor", "targetColor": "30 30 30 245"}, {"nativeAnimation": "statusBarStyle", "statusBarStyle": "light"}]}, "cloudIn": {"actions": [{"animationNode": "cloud-in", "targetNode": "^cloud-", "removedOnCompletion": false, "blendInDuration": 0.0, "fillsForward": true}]}, "cloudOut": {"actions": [{"animationNode": "cloud-out", "targetNode": "^cloud-", "removedOnCompletion": false, "blendInDuration": 0.2, "fillsForward": true}]}, "cloudSwap": {"actions": [{"animationNode": "cloud-out", "targetNode": "^old-cloud-", "removedOnCompletion": false, "blendInDuration": 0.2, "fillsForward": true}]}}}