{
  "animations": {
    "sunIn": {
      "actions": [
        {
          "nativeAnimation": "planeColor",
          "targetColor": "255 178 0"
        },
        {
          "nativeAnimation": "planeNoiseIntensity",
          "intensity": 0.4
        },
        {
          "nativeAnimation": "fogColor",
          "targetColor": "255 178 0 255"
        },
        {
          "nativeAnimation": "statusBarStyle",
          "statusBarStyle": "dark"
        }
      ]
    },
    "moonIn": {
      "actions": [
        {
          "nativeAnimation": "planeColor",
          "targetColor": "30 30 30"
        },
        {
          "nativeAnimation": "planeNoiseIntensity",
          "intensity": 0.5
        },
        {
          "nativeAnimation": "fogColor",
          "targetColor": "30 30 30 245"
        },
        {
          "nativeAnimation": "statusBarStyle",
          "statusBarStyle": "light"
        }
      ]
    },
  }
}
