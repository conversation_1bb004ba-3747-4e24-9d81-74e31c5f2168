import { useState, useEffect } from 'react';

// Demo data based on the iOS app's DemoData.json
const demoWeatherData = {
  current: {
    location: 'Seattle, WA',
    temperature: 53,
    feelsLike: 53,
    condition: 'partly-cloudy',
    description: 'Partly Cloudy',
    humidity: 90,
    windSpeed: 15,
    windDirection: 0,
    pressure: 25.0,
    visibility: 10000,
    uvIndex: 5,
    cloudCover: 50,
    dewPoint: 63.8,
    timestamp: Date.now()
  },
  hourly: [
    { time: '12 PM', temp: 53, condition: 'partly-cloudy', precipitation: 0 },
    { time: '1 PM', temp: 55, condition: 'cloudy', precipitation: 10 },
    { time: '2 PM', temp: 57, condition: 'rain', precipitation: 60 },
    { time: '3 PM', temp: 56, condition: 'rain', precipitation: 80 },
    { time: '4 PM', temp: 54, condition: 'cloudy', precipitation: 20 },
    { time: '5 PM', temp: 52, condition: 'partly-cloudy', precipitation: 0 },
    { time: '6 PM', temp: 50, condition: 'clear', precipitation: 0 },
    { time: '7 PM', temp: 48, condition: 'clear', precipitation: 0 }
  ],
  daily: [
    { day: 'Today', high: 60, low: 45, condition: 'partly-cloudy', precipitation: 30 },
    { day: 'Tomorrow', high: 65, low: 48, condition: 'sunny', precipitation: 0 },
    { day: 'Wednesday', high: 58, low: 42, condition: 'rain', precipitation: 80 },
    { day: 'Thursday', high: 62, low: 46, condition: 'cloudy', precipitation: 20 },
    { day: 'Friday', high: 67, low: 50, condition: 'sunny', precipitation: 0 },
    { day: 'Saturday', high: 64, low: 48, condition: 'partly-cloudy', precipitation: 10 },
    { day: 'Sunday', high: 61, low: 45, condition: 'cloudy', precipitation: 40 }
  ]
};

export function useWeatherData(location) {
  const [weatherData, setWeatherData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchWeatherData = async () => {
      try {
        setLoading(true);
        setError(null);

        // For demo purposes, we'll use the demo data
        // In a real app, you would fetch from a weather API like OpenWeatherMap
        if (location) {
          // Simulate API call delay
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Update location in demo data
          const updatedData = {
            ...demoWeatherData,
            current: {
              ...demoWeatherData.current,
              location: `${location.city || 'Unknown'}, ${location.region || location.country || ''}`
            }
          };
          
          setWeatherData(updatedData);
        } else {
          // Use default demo data
          await new Promise(resolve => setTimeout(resolve, 1000));
          setWeatherData(demoWeatherData);
        }
      } catch (err) {
        setError('Failed to fetch weather data');
        console.error('Weather fetch error:', err);
        // Fallback to demo data on error
        setWeatherData(demoWeatherData);
      } finally {
        setLoading(false);
      }
    };

    fetchWeatherData();
  }, [location]);

  return { weatherData, loading, error };
}

// Real weather API integration (commented out for demo)
/*
const WEATHER_API_KEY = 'your-openweathermap-api-key';
const WEATHER_API_BASE = 'https://api.openweathermap.org/data/2.5';

async function fetchRealWeatherData(lat, lon) {
  const currentResponse = await fetch(
    `${WEATHER_API_BASE}/weather?lat=${lat}&lon=${lon}&appid=${WEATHER_API_KEY}&units=imperial`
  );
  
  const forecastResponse = await fetch(
    `${WEATHER_API_BASE}/forecast?lat=${lat}&lon=${lon}&appid=${WEATHER_API_KEY}&units=imperial`
  );
  
  const currentData = await currentResponse.json();
  const forecastData = await forecastResponse.json();
  
  return {
    current: {
      location: `${currentData.name}, ${currentData.sys.country}`,
      temperature: Math.round(currentData.main.temp),
      feelsLike: Math.round(currentData.main.feels_like),
      condition: mapWeatherCondition(currentData.weather[0].main.toLowerCase()),
      description: currentData.weather[0].description,
      humidity: currentData.main.humidity,
      windSpeed: Math.round(currentData.wind.speed),
      windDirection: currentData.wind.deg,
      pressure: currentData.main.pressure,
      visibility: currentData.visibility,
      uvIndex: 0, // Would need separate UV API call
      cloudCover: currentData.clouds.all,
      dewPoint: calculateDewPoint(currentData.main.temp, currentData.main.humidity),
      timestamp: currentData.dt * 1000
    },
    hourly: forecastData.list.slice(0, 8).map(item => ({
      time: new Date(item.dt * 1000).toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        hour12: true 
      }),
      temp: Math.round(item.main.temp),
      condition: mapWeatherCondition(item.weather[0].main.toLowerCase()),
      precipitation: Math.round((item.pop || 0) * 100)
    })),
    daily: [] // Would need daily forecast API or process 5-day forecast
  };
}

function mapWeatherCondition(condition) {
  const mapping = {
    'clear': 'clear',
    'clouds': 'cloudy',
    'rain': 'rain',
    'drizzle': 'drizzle',
    'thunderstorm': 'thunderstorm',
    'snow': 'snow',
    'mist': 'fog',
    'fog': 'fog',
    'haze': 'haze'
  };
  return mapping[condition] || 'clear';
}

function calculateDewPoint(temp, humidity) {
  const a = 17.27;
  const b = 237.7;
  const alpha = ((a * temp) / (b + temp)) + Math.log(humidity / 100);
  return (b * alpha) / (a - alpha);
}
*/
