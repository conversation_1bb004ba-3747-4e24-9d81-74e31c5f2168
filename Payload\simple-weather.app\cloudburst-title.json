{"v": "5.7.4", "fr": 30, "ip": 0, "op": 41, "w": 375, "h": 375, "nm": "title", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [187.5, 187.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [96, 96], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill-primary", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 0, "s": [25, 25]}, {"t": 2, "s": [100, 100]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 3, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "debris 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 296, "ix": 10}, "p": {"a": 0, "k": [187.5, 187.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [6, 18], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill-primary", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [0, -12], "to": [0, -3.916], "ti": [0, 3.917]}, {"t": 7, "s": [0, -131]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 3, "s": [100, 60]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 5, "s": [100, 120]}, {"t": 7, "s": [60, 20]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 3, "op": 8, "st": 3, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "debris 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 184, "ix": 10}, "p": {"a": 0, "k": [187.5, 187.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [8, 24], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill-primary", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 1, "s": [0, -12], "to": [0, -3.916], "ti": [0, 3.917]}, {"t": 5, "s": [0, -131]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 1, "s": [100, 100]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 3, "s": [100, 120]}, {"t": 5, "s": [60, 20]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 6, "st": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "debris 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 128, "ix": 10}, "p": {"a": 0, "k": [187.5, 187.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [8, 24], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill-primary", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2, "s": [0, -12], "to": [0, -3.916], "ti": [0, 3.917]}, {"t": 6, "s": [0, -104]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 2, "s": [100, 100]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 4, "s": [100, 120]}, {"t": 6, "s": [60, 20]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 2, "op": 7, "st": 2, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "debris", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [187.5, 187.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [10, 24], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill-primary", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [0, -12], "to": [0, -3.916], "ti": [0, 3.917]}, {"t": 4, "s": [0, -104]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 0, "s": [100, 100]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 2, "s": [100, 120]}, {"t": 4, "s": [80, 20]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 5, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "pop", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 680, "ix": 10}, "p": {"a": 0, "k": [165.75, 182.25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [187.5, 187.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [60, 60, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 375, "h": 375, "ip": 30, "op": 60, "st": 30, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "pop", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 474, "ix": 10}, "p": {"a": 0, "k": [240.5, 180.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [187.5, 187.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [40, 40, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 375, "h": 375, "ip": 23, "op": 53, "st": 23, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "pop", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 224, "ix": 10}, "p": {"a": 0, "k": [76.75, 187, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [187.5, 187.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [50, 50, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 375, "h": 375, "ip": 20, "op": 50, "st": 20, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "pop", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 300, "ix": 10}, "p": {"a": 0, "k": [199.75, 183, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [187.5, 187.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [75, 75, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 375, "h": 375, "ip": 18, "op": 48, "st": 18, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "pop", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 112, "ix": 10}, "p": {"a": 0, "k": [271, 187, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [187.5, 187.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [66, 66, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 375, "h": 375, "ip": 14, "op": 44, "st": 14, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "pop", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [44, 187, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [187.5, 187.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 375, "h": 375, "ip": 10, "op": 40, "st": 10, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 1, "nm": "C", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46.5, 189.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 12, "s": [110, 110, 100]}, {"t": 14, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "f", "pt": {"a": 0, "k": {"i": [[1.418, 6.669], [-10.829, 5.497], [0.131, -3.513], [-2.447, -1.416], [-3.492, 0.549], [0.94, 4.835], [6.813, -1.448], [-5.139, -24.177], [-10.359, 2.202], [2.265, 10.653], [1.519, 2.098], [4.76, -1.011]], "o": [[-2.56, -12.042], [-0.465, 3.778], [0.075, 3.566], [2.447, 1.416], [-1.304, -4.757], [-1.049, -5.393], [-17.079, 3.63], [3.072, 14.451], [9.799, -2.083], [-0.571, -2.686], [-1.51, 5.743], [-5.413, 1.15]], "v": [[528.531, 548.997], [543.208, 511.12], [540.792, 524.994], [544.209, 531.433], [553.97, 532.843], [555.537, 509.467], [541.763, 500.196], [515.861, 557.306], [538.342, 574.505], [552.879, 553.407], [549.658, 546.056], [538.757, 557.183]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "sw": 1080, "sh": 1080, "sc": "#000000", "ip": 12, "op": 312, "st": 12, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 1, "nm": "L", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [80, 188.229, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 22, "s": [110, 110, 100]}, {"t": 24, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "f", "pt": {"a": 0, "k": {"i": [[2.315, 0.57], [0, 0], [0, 0], [0.091, -1.005], [0.189, -1.432], [0.6, -3.9], [0.116, -0.749], [0, 0], [0.617, -5.726], [0, 0], [0, 0], [-1.794, -2.092], [0, 0], [-1.432, -0.877], [-1.431, 0.304], [-2.665, 1.188], [-0.343, 0.152], [-1.205, 0.393], [-0.585, -0.11], [0, 0], [0, 0], [1.502, 1.732], [2.114, -0.449], [2.408, -1.225], [-0.791, 4.984], [0, 0], [-0.365, 3.237], [2.112, 1.824], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0.031, 0.39], [-0.09, 0.99], [-0.379, 2.863], [-0.112, 0.732], [0, 0], [-1.113, 7.222], [0, 0], [0, 0], [-0.262, 3.102], [0, 0], [1.564, 1.815], [1.44, 0.882], [2.39, -0.508], [0.352, -0.157], [1.49, -0.659], [1.233, -0.402], [0, 0], [0, 0], [-1.021, -3.173], [-1.517, -1.749], [-2.698, 0.573], [0.531, -4.375], [0, 0], [0.796, -5.016], [0.36, -3.194], [0, 0], [0, 0], [-1.592, -1.334]], "v": [[531.408, 512.374], [530.506, 512.152], [530.58, 513.077], [530.492, 515.178], [530.067, 518.838], [528.541, 529.212], [528.199, 531.434], [528.198, 531.437], [525.254, 552.26], [525.254, 552.268], [525.253, 552.276], [527.297, 560.029], [527.298, 560.03], [531.777, 564.121], [536.088, 565.086], [544.023, 562.104], [545.066, 561.64], [549.154, 559.995], [551.889, 559.553], [552.995, 559.762], [552.65, 558.691], [548.875, 551.175], [543.401, 548.958], [535.796, 551.978], [537.934, 537.41], [538.12, 536.231], [540.028, 523.278], [538.009, 515.551], [538.005, 515.548], [538.001, 515.545]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 2"}], "sw": 1080, "sh": 1080, "sc": "#000000", "ip": 22, "op": 322, "st": 22, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 1, "nm": "O", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [99, 178.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 18, "s": [110, 110, 100]}, {"t": 20, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[2.57, -0.546], [2.128, -5.526], [-1.285, -6.046], [-1.974, -1.56], [-2.656, 0.565], [-1.963, 5.547], [1.34, 6.308], [1.801, 1.563]], "o": [[-4.805, 1.021], [-2.159, 5.606], [0.697, 3.281], [2.01, 1.588], [4.759, -1.012], [1.993, -5.633], [-0.6, -2.823], [-1.836, -1.594]], "v": [[540.674, 520.354], [530.085, 531.624], [528.435, 550.155], [532.449, 557.524], [539.605, 558.994], [549.813, 547.743], [551.066, 528.842], [547.428, 522.07]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 3"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.224, 0.047], [-0.01, 0.001], [-0.029, -0.037], [-0.229, -1.076], [0.843, -3.57], [0.577, -1.071], [0.24, -0.249], [0.07, -0.045], [-0.003, 0.001], [0.303, 0.341], [0.281, 1.323], [-1.039, 3.309], [-0.659, 0.985]], "o": [[0.019, -0.004], [0.015, 0.013], [0.19, 0.242], [0.773, 3.638], [-0.421, 1.781], [-0.288, 0.536], [-0.118, 0.123], [-0.069, 0.044], [-0.173, 0.037], [-0.353, -0.397], [-0.776, -3.648], [0.518, -1.65], [0.705, -1.056]], "v": [[541.681, 529.064], [541.725, 529.057], [541.791, 529.13], [542.459, 531], [542.103, 542.604], [540.561, 546.966], [539.757, 548.137], [539.473, 548.38], [539.375, 548.43], [538.693, 548.118], [537.661, 545.615], [538.385, 534.525], [540.206, 530.503]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 4"}], "sw": 1080, "sh": 1080, "sc": "#000000", "ip": 18, "op": 318, "st": 18, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 1, "nm": "U", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [130.875, 190.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 27, "s": [110, 110, 100]}, {"t": 29, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "f", "pt": {"a": 0, "k": {"i": [[2.065, 0.409], [0, 0], [0, 0], [0.093, -1.213], [0.245, -1.628], [0.422, -2.445], [0.26, -1.564], [0.331, -3.981], [-0.602, -2.831], [-2.258, -1.718], [-3.394, 0.721], [-2.809, 7.776], [-0.406, 10.03], [0, 0], [0.527, 1.564], [1.458, 0.759], [1.949, 0.349], [0, 0], [0, 0], [0.241, -4.49], [1.114, -6.302], [0, 0], [1.067, -2.476], [0.828, -0.177], [0.234, 1.104], [-0.197, 1.995], [-0.448, 2.495], [-0.273, 1.447], [-0.476, 3.741], [2.878, 1.741]], "o": [[0, 0], [0, 0], [0.111, 0.592], [-0.092, 1.2], [-0.306, 2.033], [-0.255, 1.479], [-0.687, 4.135], [-0.331, 3.969], [0.758, 3.57], [2.274, 1.729], [5.726, -1.217], [3.058, -8.402], [0, 0], [0.087, -2.667], [-0.542, -1.61], [-1.857, -0.969], [0, 0], [0, 0], [0.356, 1.676], [-0.24, 4.469], [0, 0], [-1.19, 6.778], [-1.068, 2.477], [-0.792, 0.168], [-0.235, -1.105], [0.196, -1.98], [0.244, -1.362], [0.721, -3.83], [0.503, -3.511], [-1.65, -1.012]], "v": [[529.088, 514.923], [528.118, 514.731], [528.3, 515.703], [528.325, 518.42], [527.804, 522.691], [526.68, 529.464], [525.9, 534.041], [524.231, 546.481], [524.482, 556.964], [529.026, 565.048], [537.618, 566.624], [550.347, 553.608], [555.288, 523.356], [555.288, 523.351], [554.715, 517.081], [551.761, 513.626], [545.048, 511.526], [544.068, 511.35], [544.276, 512.323], [544.529, 521.773], [542.564, 538.155], [542.564, 538.156], [539.206, 551.532], [536.348, 554.853], [534.481, 553.471], [534.455, 548.773], [535.471, 541.991], [536.255, 537.763], [538.213, 526.158], [535.331, 517.284]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 5"}], "sw": 1080, "sh": 1080, "sc": "#000000", "ip": 27, "op": 327, "st": 27, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 1, "nm": "D", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [167.75, 181.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 32, "s": [110, 110, 100]}, {"t": 34, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[4.201, -0.893], [2.794, -1.685], [0.792, -0.503], [0.327, -0.205], [0.428, -0.133], [0, 0], [0, 0], [-1.246, -1.214], [0.746, -4.139], [0, 0], [0.704, -4.26], [0, 0], [-0.274, -1.719], [-1.303, -1.3], [0, 0], [-3.719, 0.79], [-2.418, 2.848], [-1.85, 4.21], [1.772, 8.336], [2.457, 1.967]], "o": [[-3.453, 0.734], [-0.947, 0.571], [-0.375, 0.238], [-1.057, 0.661], [0, 0], [0, 0], [0.635, 1.705], [-0.411, 2.516], [0, 0], [-0.832, 4.614], [0, 0], [-0.447, 2.737], [0.28, 1.761], [0, 0], [3.889, 3.85], [2.62, -0.557], [2.434, -2.866], [3.692, -8.402], [-0.83, -3.905], [-2.483, -1.987]], "v": [[539.467, 514.513], [529.788, 518.754], [527.161, 520.393], [526.106, 521.06], [523.883, 522.275], [523.207, 522.485], [523.453, 523.148], [526.529, 527.753], [524.696, 538.16], [524.566, 538.879], [522.128, 552.773], [522.127, 552.774], [521.776, 559.305], [524.107, 563.716], [524.109, 563.718], [536.05, 568.604], [543.736, 563.173], [550.291, 552.319], [554.285, 525.32], [549.47, 516.272]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 7"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.247, 2.01], [1.026, 1.61], [-1.629, 0.346], [-0.513, -0.408], [-0.27, -1.266], [0.442, -2.778], [1.162, -3.001], [3.9, -2.641], [-0.709, 4.298], [0, 0]], "o": [[0.282, -2.163], [1.711, -0.992], [1.369, -0.291], [0.543, 0.432], [0.365, 1.719], [-0.44, 2.766], [-2.133, 5.503], [0.46, -3.266], [0, 0], [0.75, -4.535]], "v": [[535.856, 533.431], [535.001, 527.712], [540.006, 525.414], [542.682, 525.725], [543.885, 528.215], [543.764, 535.133], [541.334, 543.973], [532.093, 557.249], [534, 545.181], [534.17, 544.15]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 8"}], "sw": 1080, "sh": 1080, "sc": "#000000", "ip": 32, "op": 332, "st": 32, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 1, "nm": "B", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [202.5, 185, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 20, "s": [110, 110, 100]}, {"t": 22, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[1.217, 5.726], [4.626, 0.176], [0.997, 4.695], [2.066, 1.187], [3.885, -0.826], [3.381, -2.055], [0.789, -0.494], [0.759, -0.45], [0.727, -0.26], [0, 0], [0, 0], [-1.343, -1.184], [0.972, -5.551], [0, 0], [0.553, -3.799], [-0.216, -1.92], [-1.452, -1.418], [-3.965, 0], [-0.684, 0.145], [-4.318, 6.115]], "o": [[-1.011, -4.748], [8.118, -5.964], [-0.597, -2.808], [-2.289, -1.315], [-4.888, 1.039], [-0.794, 0.483], [-0.893, 0.56], [-1.249, 0.74], [0, 0], [0, 0], [0.763, 1.727], [-0.459, 3.094], [0, 0], [-1.079, 6.162], [-0.502, 3.401], [0.224, 1.999], [3.34, 3.286], [0.744, 0], [5.414, -1.151], [4.521, -6.404]], "v": [[558.693, 539.538], [549.301, 531.359], [558.154, 513.139], [554.141, 507.117], [544.838, 506.38], [532.061, 511.965], [529.7, 513.428], [527.207, 514.965], [524.35, 516.411], [523.576, 516.687], [523.908, 517.439], [527.171, 521.95], [524.834, 535.775], [524.652, 536.815], [522.007, 552.621], [521.472, 560.287], [523.863, 565.161], [535.802, 570.445], [537.951, 570.229], [553.492, 558.625]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 9"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[3.646, -2.274], [-0.08, 0.621], [-0.1, 0.637], [1.244, 1.412], [-0.759, 0.461], [0, 0], [-0.703, 0.403], [-1.493, 0.317], [-0.437, -0.372], [-0.145, -0.683], [1.945, -2.381]], "o": [[0.1, -0.722], [0.113, -0.864], [0.393, -3.02], [0.731, -0.412], [0, 0], [0.702, -0.426], [1.274, -0.731], [0.763, -0.163], [0.365, 0.309], [0.364, 1.713], [-1.953, 2.39]], "v": [[535.85, 533.021], [536.118, 531.001], [536.429, 528.745], [535.23, 522.487], [537.468, 521.163], [537.708, 521.017], [539.823, 519.756], [544.244, 517.829], [546.054, 518.143], [546.822, 519.638], [544.405, 525.897]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 10"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[-0.952, 5.951], [-2.156, 0.458], [-0.769, -0.49], [-0.136, -0.645], [0.633, -1.669], [1.343, -1.681], [4.221, -1.522]], "o": [[3.13, -2.149], [1.229, -0.261], [0.853, 0.544], [0.26, 1.221], [-0.626, 1.653], [-2.931, 3.676], [0.532, -4.014]], "v": [[534.424, 542.888], [542.503, 538.902], [545.515, 539.247], [546.848, 541.319], [546.277, 545.736], [543.269, 550.832], [532.026, 559.001]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 11"}], "sw": 1080, "sh": 1080, "sc": "#000000", "ip": 20, "op": 320, "st": 20, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 1, "nm": "U", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [240.25, 179.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 25, "s": [110, 110, 100]}, {"t": 27, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "f", "pt": {"a": 0, "k": {"i": [[1.766, 0.35], [0, 0], [0, 0], [0.079, -1.037], [0.21, -1.392], [0.361, -2.09], [0.222, -1.337], [0.284, -3.403], [-0.514, -2.421], [-1.931, -1.469], [-2.902, 0.617], [-2.401, 6.648], [-0.347, 8.575], [0, 0], [0.451, 1.337], [1.247, 0.649], [1.666, 0.299], [0, 0], [0, 0], [0.206, -3.838], [0.952, -5.388], [0, 0], [0.912, -2.117], [0.708, -0.151], [0.2, 0.944], [-0.169, 1.705], [-0.383, 2.133], [-0.233, 1.237], [-0.408, 3.198], [2.46, 1.488]], "o": [[0, 0], [0, 0], [0.095, 0.506], [-0.078, 1.026], [-0.262, 1.738], [-0.218, 1.264], [-0.588, 3.535], [-0.282, 3.393], [0.649, 3.052], [1.943, 1.478], [4.895, -1.04], [2.615, -7.183], [0, 0], [0.074, -2.28], [-0.464, -1.377], [-1.587, -0.828], [0, 0], [0, 0], [0.305, 1.433], [-0.205, 3.821], [0, 0], [-1.017, 5.794], [-0.913, 2.117], [-0.677, 0.144], [-0.201, -0.944], [0.167, -1.693], [0.209, -1.165], [0.617, -3.274], [0.43, -3.001], [-1.411, -0.866]], "v": [[530.463, 520.242], [529.633, 520.077], [529.789, 520.908], [529.811, 523.231], [529.366, 526.882], [528.404, 532.672], [527.738, 536.585], [526.31, 547.22], [526.525, 556.182], [530.41, 563.093], [537.756, 564.44], [548.637, 553.312], [552.861, 527.451], [552.861, 527.446], [552.371, 522.087], [549.846, 519.133], [544.107, 517.337], [543.27, 517.187], [543.446, 518.019], [543.662, 526.097], [541.983, 540.102], [541.983, 540.103], [539.113, 551.538], [536.67, 554.377], [535.074, 553.195], [535.052, 549.179], [535.919, 543.381], [536.59, 539.767], [538.264, 529.846], [535.801, 522.261]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 6"}], "sw": 1080, "sh": 1080, "sc": "#000000", "ip": 25, "op": 325, "st": 25, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 1, "nm": "R", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [269.5, 187.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 16, "s": [120, 120, 100]}, {"t": 18, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[3.87, -0.823], [2.724, -1.786], [0.553, -0.376], [0.575, -0.366], [0.655, -0.139], [0, 0], [0, 0], [-1.456, -1.253], [0.726, -4.063], [0.284, -1.625], [0.258, -4.477], [-2.013, -1.178], [-2.038, -0.207], [0, 0], [0, 0], [-0.024, 1.266], [-0.167, 1.694], [-0.543, 3.486], [-3.922, -5.083], [-1.872, -2.223], [-0.695, -0.747], [-0.401, -0.295], [-1.357, 0.099], [-1.152, 0.856], [-1.273, 2.562], [0.738, 0.561], [0.98, 0.929], [2.478, 2.711], [2.723, 3.354], [1.681, 7.908], [2.566, 1.704]], "o": [[-4.116, 0.875], [-0.645, 0.423], [-0.644, 0.438], [-1.059, 0.674], [0, 0], [0, 0], [0.752, 1.941], [-0.363, 2.668], [-0.278, 1.554], [-1.056, 6.041], [-0.268, 3.754], [1.515, 0.922], [0, 0], [0, 0], [-0.142, -0.598], [0.023, -1.253], [0.285, -2.892], [2.22, 3.479], [2.295, 2.975], [0.936, 1.111], [0.678, 0.729], [1.4, 1.03], [1.342, -0.098], [2.303, -1.712], [-0.922, -0.124], [-0.697, -0.529], [-1.958, -1.855], [-4.671, -5.111], [9.168, -5.959], [-0.745, -3.504], [-2.568, -1.706]], "v": [[543.828, 509.081], [533.598, 513.697], [531.81, 514.896], [529.996, 516.105], [527.456, 517.35], [526.687, 517.513], [526.971, 518.246], [530.459, 523.201], [528.71, 533.627], [527.86, 538.414], [525.538, 555.202], [528.357, 562.404], [534.719, 564.417], [535.656, 564.512], [535.438, 563.597], [535.258, 560.781], [535.553, 556.321], [536.848, 546.548], [547.014, 560.618], [553.452, 568.658], [555.921, 571.477], [557.582, 573.063], [561.714, 574.413], [565.446, 572.855], [570.852, 566.447], [568.027, 565.37], [565.491, 563.159], [558.657, 556.081], [546.342, 541.829], [558.629, 518.523], [553.602, 510.518]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 12"}, {"inv": false, "mode": "s", "pt": {"a": 0, "k": {"i": [[0.111, 1.262], [1.062, 0.958], [0, 0], [-2.16, 0.459], [-0.526, -0.352], [-0.195, -0.919], [2.114, -2.849], [2.352, -1.311], [-0.134, 0.794], [0, 0], [-0.147, 0.954], [0, 0]], "o": [[-0.114, -1.295], [0, 0], [1.808, -1.224], [1.302, -0.277], [0.529, 0.354], [0.531, 2.5], [-1.806, 2.434], [0.137, -0.847], [0, 0], [0.184, -1.089], [0, 0], [0.24, -1.573]], "v": [[539.879, 525.731], [538.254, 522.338], [538.47, 522.192], [544.162, 519.341], [546.811, 519.588], [547.902, 521.488], [545.155, 529.923], [538.584, 535.812], [538.993, 533.348], [539.037, 533.082], [539.543, 530.007], [539.543, 530.006]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 13"}], "sw": 1080, "sh": 1080, "sc": "#000000", "ip": 16, "op": 316, "st": 16, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 1, "nm": "S", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [302.25, 182, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 30, "s": [110, 110, 100]}, {"t": 32, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[-1.999, 0.303], [0, 0], [0, 0], [-0.123, 1.151], [-0.232, 1.325], [0, 0], [-0.138, 1.081], [0.171, 0.889], [0, 0], [1.654, 1.068], [2.244, -0.474], [2.53, -3.49], [-0.845, -3.972], [-2.51, -1.486], [-0.357, -0.206], [-0.655, -0.465], [-0.068, -0.321], [0.274, -0.301], [0.942, -0.2], [0.557, 2.44], [0, 0], [0, 0], [-0.694, -3.266], [-2.414, -1.232], [-3.48, 0.74], [1.273, 5.991], [2.586, 1.6], [0.215, 0.132], [0, 0], [0.796, 0.648], [0.153, 0.718], [-0.22, 0.858], [-0.521, 0.903], [-0.642, 0.654], [0, 0], [0.044, -1.23], [0, 0], [-0.464, -0.839], [-0.793, -0.416]], "o": [[0, 0], [0, 0], [-0.102, -0.375], [0.117, -1.098], [0, 0], [0.209, -1.19], [0.147, -1.147], [0, 0], [-0.357, -1.843], [-1.671, -1.079], [-4.148, 0.827], [-2.524, 3.482], [0.769, 3.617], [0.391, 0.232], [0.873, 0.503], [0.947, 0.673], [0.076, 0.356], [-0.305, 0.335], [-3.599, 0.765], [0, 0], [0, 0], [-2.237, 2.598], [0.558, 2.626], [2.393, 1.221], [7.883, -1.675], [-0.81, -3.81], [-0.225, -0.139], [0, 0], [-1.099, -0.674], [-0.94, -0.764], [-0.093, -0.438], [0.216, -0.839], [0.487, -0.846], [0, 0], [-0.358, 1.398], [0, 0], [-0.031, 1.084], [0.472, 0.855], [1.574, 0.861]], "v": [[550.97, 538.758], [552.457, 538.532], [552.06, 537.082], [552.067, 534.804], [552.632, 531.107], [552.695, 530.741], [553.255, 527.252], [553.322, 524.033], [553.322, 524.031], [550.017, 519.47], [543.934, 518.296], [533.485, 525.459], [530.352, 537.27], [536.774, 544.461], [537.897, 545.115], [540.194, 546.527], [541.535, 547.988], [541.255, 548.953], [539.422, 549.845], [532.549, 546.316], [531.958, 543.733], [530.23, 545.74], [528.108, 554.561], [532.699, 560.534], [541.652, 561.327], [552.008, 547.176], [545.403, 539.65], [544.744, 539.244], [544.742, 539.243], [541.877, 537.326], [540.287, 535.158], [540.439, 533.189], [541.556, 530.519], [543.269, 528.239], [543.128, 528.788], [542.324, 533.031], [542.324, 533.04], [542.882, 535.935], [544.871, 537.744]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 14"}], "sw": 1080, "sh": 1080, "sc": "#000000", "ip": 30, "op": 330, "st": 30, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 1, "nm": "T", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [334.25, 179.302, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [540, 540, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 23, "s": [110, 110, 100]}, {"t": 25, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "f", "pt": {"a": 0, "k": {"i": [[1.108, 0.096], [0, 0], [0, 0], [0.136, -0.962], [0.039, -0.268], [0.08, -0.726], [-0.088, -0.892], [-0.575, -0.63], [-2.29, 0.019], [-0.231, 0], [-0.953, -0.029], [0, 0], [-0.53, -0.01], [0.558, -7.493], [0, 0], [-2.255, -1.391], [-2.337, -0.314], [0, 0], [0, 0], [0.013, 0.97], [-0.098, 1.377], [-0.533, 3.921], [-1.515, 8.69], [0, 0], [0.227, 0.673], [0.174, 0.206], [-2.898, 0.125], [-1.249, -0.068], [-0.437, -0.136], [0, 0], [0, 0], [0.736, 1.891], [0.972, 1.219], [1.204, 0.273], [1.255, -0.097], [4.298, -0.58], [1.126, -0.149], [2.206, -0.208]], "o": [[0, 0], [0, 0], [0, 0.701], [-0.036, 0.257], [-0.105, 0.719], [-0.11, 0.999], [0.087, 0.882], [1.795, 1.967], [0.226, -0.002], [0.996, 0], [0, 0], [0.576, 0.018], [-2.422, 11.08], [0, 0], [-0.29, 4.369], [2.068, 1.276], [0, 0], [0, 0], [-0.118, -0.406], [-0.013, -0.956], [0.196, -2.754], [1.066, -7.84], [0, 0], [0.22, -1.315], [-0.096, -0.285], [2.595, -0.733], [1.654, -0.071], [1.272, 0.069], [0, 0], [0, 0], [-0.414, -1.574], [-0.732, -1.876], [-0.951, -1.193], [-1.171, -0.265], [-3.06, 0.237], [-1.142, 0.154], [-2.69, 0.355], [-2.22, 0.21]], "v": [[524.893, 518.893], [524.167, 518.83], [524.167, 519.56], [523.929, 522.084], [523.815, 522.873], [523.52, 525.069], [523.438, 527.964], [524.341, 530.341], [530.458, 532.273], [531.143, 532.269], [534.09, 532.327], [534.262, 532.332], [535.927, 532.377], [530.51, 565.389], [530.51, 565.395], [533.801, 573.746], [541.581, 576.464], [542.601, 576.601], [542.313, 575.614], [542.107, 573.554], [542.237, 570.038], [543.368, 559.872], [547.539, 533.825], [547.541, 533.82], [547.605, 530.959], [547.188, 530.232], [555.881, 528.999], [560.322, 529.006], [562.944, 529.336], [564.096, 529.693], [563.789, 528.526], [562.03, 523.069], [559.448, 518.139], [556.199, 516.061], [552.518, 515.955], [540.904, 517.346], [537.491, 517.804], [530.007, 518.691]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 15"}], "sw": 1080, "sh": 1080, "sc": "#000000", "ip": 23, "op": 323, "st": 23, "bm": 0}], "markers": []}