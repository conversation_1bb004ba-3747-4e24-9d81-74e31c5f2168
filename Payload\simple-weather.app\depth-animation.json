{
  "animations": {
    "sunIn": {
      "actions": [
        {
          "nativeAnimation": "planeColor",
          "targetColor": "192 192 192"
        },
        {
          "nativeAnimation": "planeNoiseIntensity",
          "intensity": 0.0
        },
        {
          "nativeAnimation": "fogColor",
          "targetColor": "0 0 0 0"
        },
        {
          "nativeAnimation": "crossFadeNodes",
          "sourceNode": "plane-night",
          "targetNode": "plane-day"
        },
        {
          "nativeAnimation": "statusBarStyle",
          "statusBarStyle": "dark"
        }
      ]
    },
    "moonIn": {
      "actions": [
        {
          "nativeAnimation": "planeColor",
          "targetColor": "0 0 0"
        },
        {
          "nativeAnimation": "planeNoiseIntensity",
          "intensity": 0.0
        },
        {
          "nativeAnimation": "fogColor",
          "targetColor": "0 0 0 0"
        },
        {
          "nativeAnimation": "crossFadeNodes",
          "sourceNode": "plane-day",
          "targetNode": "plane-night"
        },
        {
          "nativeAnimation": "statusBarStyle",
          "statusBarStyle": "light"
        }
      ]
    },
  }
}
